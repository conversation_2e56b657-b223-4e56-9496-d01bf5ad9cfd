import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import ObjectUtil from '@/utils/ObjectUtil';
import { RouterNames } from '@/router/modules';
// HTTPS
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyPriceOverviewMatLineV2 from '@/model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewMatLineV2';
import ApplyDescription from '@/model/remote/price/api/specialapply/bill/ApplyDescription';
import SpecialApplyPriceOverviewSaveRequestV2 from '@/model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewSaveRequestV2';
import SpecialApplyMatLineScopeExwPriceOverview from '@/model/remote/price/api/specialapply/mat/SpecialApplyMatLineScopeExwPriceOverview';
import { SpecialType } from '@/model/remote/price/api/specialapply/bill/SpecialType';
// COMPONENTES
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import ThousNumber from '@/components/form/thous-number/ThousNumber.vue';
import SpecialApplyDescriptionDrawer from './SpecialApplyDescriptionDrawer.vue';
import Dialog from '@/components/dialog/Dialog';
import SpecialSubmitDialog from '../drawer/SpecialSubmitDialog.vue';
import SpecialApplyPriceOverviewApi from '@/http/price/controller/specialapply/price/SpecialApplyPriceOverviewApi';
import CalMatLineOtherFee from '@/model/remote/price/api/specialapply/priceoverview/CalMatLineOtherFee';
import SpecialApplyCostEstimate from './SpecialApplyCostEstimate.vue';
@Component({
  name: 'TabStep1',
  components: { NumberInput, ThousNumber, SpecialApplyDescriptionDrawer, SpecialApplyCostEstimate },
})
export default class TabStep1 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  $refs: any;
  tableData: any[] = [];
  tableSpan: any = {};
  PrecisionUtil = PrecisionUtil;
  ObjectUtil = ObjectUtil;
  entity: SpecialApplyPriceOverviewMatLineV2[] = [];
  showFillDrawer: boolean = false; // 特价申请说明抽屉是否打开
  showCostEstimate: boolean = false; // 费用估算抽屉是否打开
  specialApplyDescription: ApplyDescription = new ApplyDescription(); // 特价申请说明
  SpecialType = SpecialType;
  // 是否包含其他费用
  otherExpenses: Nullable<boolean> = null;

  // 是否显示"报价是否包含以下费用项"选项
  get showOtherExpensesOption() {
    // 当"特价类型=区域指导价调整"时，隐藏选项，系统按"否"处理
    if (this.baseEntity.specialType === 'adjustZoneGuidePrice') {
      return false;
    }
    // 当出口类型=自营，销售模式=买断时，隐藏选项，系统按"是"处理
    if (this.baseEntity.exportTypeId === 'self' && this.baseEntity.saleMode === 'buyOut') {
      return false;
    }
    // 默认显示，由用户选择
    return true;
  }

  created() {
    this.getData();
  }
  getData() {
    // 从prop初始化本地状态，默认为null，使用??避免false被修改
    this.otherExpenses = this.baseEntity.otherExpenses ?? null;
    // 当“特价类型=区域指导价调整” 时，此选项隐藏，系统按“否”处理
    if (this.baseEntity.specialType === 'adjustZoneGuidePrice') {
      this.otherExpenses = false;
    }
    // 自营-买断时，报价是否包含以下费用项默认为true，不显示
    if (this.baseEntity.exportTypeId === 'self' && this.baseEntity.saleMode === 'buyOut') {
      this.otherExpenses = true;
    }

    // 获取填写特价
    SpecialApplyBillApi.getPriceOverview(this.baseEntity.id!)
      .then((res) => {
        this.entity = res.data || [];
        this.getTableData(this.entity);
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
    // 获取特价申请说明
    SpecialApplyBillApi.getApplyDescription(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.specialApplyDescription = res.data;
          if (this.specialApplyDescription.attachment == null) {
            this.specialApplyDescription.attachment = [];
          }
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }
  doFill(command) {
    if (command === 'copy') {
      // 复制
      this.$putWindow('/data/SpecialApply/cmp/step3/SelectList.vue', {
        propsData: {
          baseEntity: this.baseEntity,
          success: (selected) => {
            SpecialApplyBillApi.getApplyDescription(selected.id!)
              .then((res) => {
                if (res.data) {
                  let params = res.data;
                  // 竞品信息
                  if (
                    this.specialApplyDescription.competitiveInformation &&
                    this.specialApplyDescription.competitiveInformation.length > 0
                  ) {
                    let arr: any = [];
                    if (params.competitiveInformation && params.competitiveInformation.length > 0) {
                      this.specialApplyDescription.competitiveInformation.forEach((item) => {
                        let findList = params.competitiveInformation.filter((i) => {
                          if (item.matLine && i.matLine) {
                            return item.matLine.matCd === i.matLine.matCd;
                          } else {
                            return false;
                          }
                        });
                        if (findList && findList.length > 0) {
                          arr.push(...findList);
                        } else {
                          arr.push(item);
                        }
                      });
                    } else {
                      arr = this.specialApplyDescription.competitiveInformation;
                    }
                    params.competitiveInformation = arr;
                  } else {
                    params.competitiveInformation = [];
                  }
                  // 预计达成目标-占有率目标
                  if (
                    this.specialApplyDescription.marketShareTarget &&
                    this.specialApplyDescription.marketShareTarget.length > 0
                  ) {
                    let arr: any = [];
                    if (params.marketShareTarget && params.marketShareTarget.length > 0) {
                      this.specialApplyDescription.marketShareTarget.forEach((item) => {
                        let findItem = params.marketShareTarget.find((i) => {
                          return item.i18ProdGroupId === i.i18ProdGroupId;
                        });
                        if (findItem) {
                          arr.push(findItem);
                        } else {
                          arr.push(item);
                        }
                      });
                    } else {
                      arr = this.specialApplyDescription.marketShareTarget;
                    }
                    params.marketShareTarget = arr;
                  } else {
                    params.marketShareTarget = [];
                  }
                  this.specialApplyDescription = params;
                  this.showFillDrawer = true;
                } else {
                  this.$message.error('未找到折扣说明');
                }
              })
              .catch((rej) => {
                this.$message.error(rej.message);
              });
          },
        },
      });
    } else {
      this.showFillDrawer = true;
    }
  }
  // 特价申请说明确认回调
  changeSpecialApplyDescription(value) {
    this.specialApplyDescription = value;
  }
  getTableData(result) {
    let arr: any = [];
    let spanArr: any = {}; // 表格数据合并
    result.forEach((item, index) => {
      if (index === 0) {
        spanArr[index] = item.exwPriceList.length;
      } else {
        let lastNum = 0;
        for (let key in spanArr) {
          lastNum = Number(key) + spanArr[key];
        }
        spanArr[lastNum] = item.exwPriceList.length;
      }
      if (item.exwPriceList && item.exwPriceList.length > 0) {
        item.exwPriceList.forEach((priceItem) => {
          // 当特价类型=样机测试/推广时，改成限量
          let finalLimitQty = priceItem.limitQty || false;
          if (this.baseEntity.specialType === SpecialType.sampleMachine) {
            finalLimitQty = true;
          }

          // 根据otherExpenses状态决定计算方式
          let calculatedData = {};

          if (this.otherExpenses) {
            // 当otherExpenses为true时，使用新的计算方式
            // 如果有综合特价(total)，则从综合特价反推设备特价
            if (priceItem.total) {
              const calculatedSpecialPrice = this.calculateSpecialPriceFromTotal({
                total: priceItem.total,
                logisticsExpenses: priceItem.logisticsExpenses || 0,
                otherExpenses: priceItem.otherExpenses || 0,
              });

              calculatedData = {
                specialPrice: calculatedSpecialPrice,
                differAmt:
                  calculatedSpecialPrice > 0 ? priceItem.exwPrice - calculatedSpecialPrice : null,
                adjustRange:
                  calculatedSpecialPrice > 0
                    ? this.calculateAdjustRange(
                        priceItem.exwPrice - calculatedSpecialPrice,
                        priceItem.exwPrice
                      )
                    : null,
              };
            } else {
              // 没有综合特价时，使用默认值
              calculatedData = {
                specialPrice: Number(priceItem.specialPrice || priceItem.exwPrice),
                adjustRange: this.calculateAdjustRange(priceItem.differAmt, priceItem.exwPrice),
              };
            }
          } else {
            // 当otherExpenses为false时，使用原有的计算方式
            calculatedData = {
              specialPrice: Number(priceItem.specialPrice || priceItem.exwPrice),
              adjustRange: this.calculateAdjustRange(priceItem.differAmt, priceItem.exwPrice),
            };
          }

          arr.push({
            ...item,
            ...priceItem,
            ...calculatedData,
            specialApplyMat: item,
            applicationScope: priceItem.applicationScope,
            mergeIndex: index,
            limitQty: finalLimitQty,
            qty: finalLimitQty && !priceItem.qty ? 1 : priceItem.qty, // 如果限量但没有数量，默认设为1
          });
        });
      }
    });
    this.tableData = arr;
    this.tableSpan = spanArr;
  }
  setTableData() {
    let arr: SpecialApplyPriceOverviewMatLineV2[] = [];
    this.tableData.forEach((item) => {
      let itemIndex = arr.findIndex((i) => {
        return i.matId === item.matId;
      });
      if (itemIndex > -1) {
        // 找到对应的物料，添加到其exwPriceList中
        let priceItem = new SpecialApplyMatLineScopeExwPriceOverview();
        priceItem.id = item.id;
        priceItem.applicationScope = item.applicationScope;
        priceItem.limitQty = item.limitQty;
        priceItem.qty = item.qty;
        priceItem.exwPrice = item.exwPrice;
        priceItem.adjustRange = item.adjustRange
          ? PrecisionUtil.floatDiv(item.adjustRange, 100)
          : item.adjustRange;
        priceItem.differAmt = item.differAmt;
        priceItem.specialPrice = Number(item.specialPrice);
        priceItem.otherExpenses = item.otherExpenses;
        priceItem.total = item.total;
        priceItem.beginDate = item.beginDate;
        priceItem.endDate = item.endDate;

        arr[itemIndex].exwPriceList.push(priceItem);
      } else {
        // 创建新的物料记录
        let matLine = new SpecialApplyPriceOverviewMatLineV2();
        matLine.matId = item.matId;
        matLine.owner = item.owner;
        matLine.line = item.line;
        matLine.matCd = item.matCd;
        matLine.i18ProdGroupId = item.i18ProdGroupId;
        matLine.i18ProdGroupName = item.i18ProdGroupName;
        matLine.prodMdlId = item.prodMdlId;
        matLine.prodMdlCode = item.prodMdlCode;
        matLine.matDesc = item.matDesc;
        matLine.matDescEn = item.matDescEn;

        // 创建价格项
        let priceItem = new SpecialApplyMatLineScopeExwPriceOverview();
        priceItem.id = item.id;
        priceItem.applicationScope = item.applicationScope;
        priceItem.limitQty = item.limitQty;
        priceItem.qty = item.qty;
        priceItem.exwPrice = item.exwPrice;
        priceItem.adjustRange = item.adjustRange
          ? PrecisionUtil.floatDiv(item.adjustRange, 100)
          : item.adjustRange;
        priceItem.differAmt = item.differAmt;
        priceItem.specialPrice = Number(item.specialPrice);
        priceItem.otherExpenses = item.otherExpenses;
        priceItem.total = item.total;
        priceItem.beginDate = item.beginDate;
        priceItem.endDate = item.endDate;

        matLine.exwPriceList = [priceItem];
        arr.push(matLine);
      }
    });
    return arr;
  }
  // 表格合并
  spanMethod({ row, column, rowIndex, columnIndex }) {
    // 对前3列进行合并：整机物料号、物料描述（营销）、物料描述（营销）_英文
    if (columnIndex <= 2) {
      if (this.tableSpan[rowIndex]) {
        return {
          rowspan: this.tableSpan[rowIndex],
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }
  // 计算调价幅度百分比
  calculateAdjustRange(differAmt, exwPrice) {
    if ((!differAmt && differAmt !== 0) || differAmt < 0 || !exwPrice || exwPrice <= 0) {
      return null;
    }
    return Number(PrecisionUtil.toFixed((differAmt / exwPrice) * 100, 2));
  }

  // 格式化调价幅度显示
  formatAdjustRange(row) {
    if (row.adjustRange === 0) {
      return '0';
    }
    if (row.adjustRange !== null && row.adjustRange !== undefined) {
      return `-${row.adjustRange}%`;
    }
    return '--';
  }

  // 台量限制变更
  limitQtyChange(row, index) {
    if (!row.limitQty) {
      // row.qty = null;
    } else {
      row.qty = row.qty || 1;
    }
  }

  // 台量数量变更
  qtyChange(row, index) {
    if (row.limitQty) {
      if (!row.qty || row.qty < 1) {
        row.qty = 1;
      } else if (row.qty > 999999) {
        row.qty = 999999;
      }
    }
  }

  // 调价差值变更（核心计算逻辑）
  differAmtChange(row, index) {
    if (row.differAmt !== null && row.differAmt !== undefined && row.differAmt !== '') {
      // 确保为数字类型
      row.differAmt = Number(row.differAmt);
      // 确保为非负整数（需求变更——调价差值能等于0）
      if (row.differAmt < 0) {
        row.differAmt = 0;
      }
      row.differAmt = Math.round(row.differAmt);

      // 限制最大值：确保调价差值不会使EXW设备特价小于1
      // EXW设备特价 = 设备指导价 - 调价差值，所以调价差值最大值 = 设备指导价 - 1
      const maxDifferAmt = row.exwPrice - 1;
      if (row.differAmt > maxDifferAmt) {
        row.differAmt = maxDifferAmt;
      }

      // 计算 EXW设备特价 = 设备指导价 - 调价差值
      row.specialPrice = PrecisionUtil.floatSub(row.exwPrice, row.differAmt);

      // 确保EXW设备特价不小于1（双重保险）
      if (row.specialPrice < 1) {
        row.specialPrice = 1;
        row.differAmt = PrecisionUtil.floatSub(row.exwPrice, row.specialPrice);
      }

      console.log('row.specialPrice', row.specialPrice);
    } else {
      // 差值为空时，特价等于指导价
      row.specialPrice = row.exwPrice;
      row.differAmt = null;
    }

    // 重新计算调价幅度
    row.adjustRange = this.calculateAdjustRange(row.differAmt, row.exwPrice);

    // 调用后端接口更新行数据
    this.calMatOtherFee(row, index);
  }

  // EXW设备特价变更
  specialPriceChange(row, index) {
    if (row.specialPrice !== null && row.specialPrice !== undefined && row.specialPrice !== '') {
      // 确保为数字类型
      row.specialPrice = Number(row.specialPrice);

      // 确保特价不超过指导价
      if (row.specialPrice > row.exwPrice) {
        row.specialPrice = row.exwPrice;
      }
      // 确保为正整数且不小于1
      if (row.specialPrice < 1) {
        row.specialPrice = 1;
      }
      row.specialPrice = Math.round(row.specialPrice);

      // 反向计算调价差值 = 设备指导价 - EXW设备特价
      row.differAmt = PrecisionUtil.floatSub(row.exwPrice, row.specialPrice);
      if (row.differAmt < 0) {
        row.differAmt = null;
      }
    } else {
      // 特价为空时，重置为指导价
      row.specialPrice = row.exwPrice;
      row.differAmt = null;
    }

    // 重新计算调价幅度
    row.adjustRange = this.calculateAdjustRange(row.differAmt, row.exwPrice);

    // 调用后端接口更新行数据
    this.calMatOtherFee(row, index);
  }

  // 综合特价变更处理
  totalChange(row: any, index: number) {
    if (!this.otherExpenses || !row.total) {
      return;
    }

    // 确保为数字类型
    row.total = Number(row.total);

    // 重新计算设备特价
    const calculatedSpecialPrice = this.calculateSpecialPriceFromTotal(row);
    row.specialPrice = calculatedSpecialPrice;

    // 重新计算调价差值和调价幅度
    if (calculatedSpecialPrice > 0 && calculatedSpecialPrice <= row.exwPrice) {
      row.differAmt = row.exwPrice - calculatedSpecialPrice;
      row.adjustRange = this.calculateAdjustRange(row.differAmt, row.exwPrice);
    } else {
      row.differAmt = null;
      row.adjustRange = null;
    }

    // 更新表格数据
    this.$set(this.tableData, index, { ...row });
    // 调用后端接口更新行数据
    this.calMatOtherFee(row, index);
  }

  calMatOtherFee(row, index) {
    let params = new CalMatLineOtherFee();
    params.owner = row.owner;
    params.matId = row.matId;
    params.line = row.line;
    params.matCd = row.matCd;
    params.i18ProdGroupId = row.i18ProdGroupId;
    params.i18ProdGroupName = row.i18ProdGroupName;
    params.prodMdlId = row.prodMdlId;
    params.prodMdlCode = row.prodMdlCode;
    params.matDesc = row.matDesc;
    params.matDescEn = row.matDescEn;
    params.limitQty = row.limitQty;
    params.qty = row.qty;

    let exwPrice = new SpecialApplyMatLineScopeExwPriceOverview();
    exwPrice.id = row.id;
    exwPrice.applicationScope = row.applicationScope;
    exwPrice.limitQty = row.limitQty;
    exwPrice.qty = row.qty;
    exwPrice.exwPrice = row.exwPrice;
    exwPrice.adjustRange = PrecisionUtil.floatDiv(row.adjustRange || 0, 100);
    exwPrice.specialPrice = row.specialPrice;
    exwPrice.differAmt = row.differAmt;
    exwPrice.otherExpenses = row.otherExpenses;
    exwPrice.logisticsExpenses = row.logisticsExpenses;
    exwPrice.total = row.total;
    params.exwPrice = exwPrice;

    SpecialApplyPriceOverviewApi.calMatOtherFee(params)
      .then((res) => {
        if (res.data && res.data.exwPrice) {
          let rowData = {
            ...row,
            ...res.data.exwPrice,
            // 重新计算调价幅度
            adjustRange: this.calculateAdjustRange(row.differAmt, row.exwPrice),
          };
          this.$set(this.tableData, index, rowData);
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }

  // 校验特价申请说明必填项
  validateSpecialApplyDescription() {
    const applyDesc = this.specialApplyDescription;
    const specialType = this.baseEntity.specialType;

    let res = true;

    // 基础校验：是否填写了特价申请说明
    if (!applyDesc || Object.keys(applyDesc).length === 0) {
      res = false;
    }

    // 重点项目类型必填项校验
    if (specialType === SpecialType.importantProjects) {
      if (!applyDesc.procurementPlan) {
        res = false;
      }
      if (applyDesc.cust && applyDesc.cust.potentialCus === null) {
        res = false;
      }
    }

    // 区域指导价调整类型必填项校验
    if (specialType === SpecialType.adjustZoneGuidePrice) {
      if (!applyDesc.applyReason || !applyDesc.applyReason.background) {
        res = false;
      }
    }

    // 样机测试类型必填项校验
    if (specialType === SpecialType.sampleMachine) {
      if (!applyDesc.background || !applyDesc.campaign) {
        res = false;
      }
    }

    // if (!res) {
    //   this.$message.error('请填写特价申请说明');
    // }

    return res;
  }

  doSave(mode?: string) {
    // 校验表单
    return new Promise((resolve, reject) => {
      // @ts-ignore
      this.$refs.form.validate((valid) => {
        if (!valid) {
          // @ts-ignore
          this.$parent.$parent.$parent.saveFail();
          // 根据模式决定错误文案
          let errorMessage = '存在必填项未填写，请填写后再保存';
          if (mode === 'next') {
            errorMessage = '存在必填项未填写，请填写后再进入下一步';
          }
          reject(new Error(errorMessage));
          return;
        }

        // 校验特价合法性（仅在有预估费用场景下）
        // if (this.otherExpenses) {
        //   for (let i = 0; i < this.tableData.length; i++) {
        //     const row = this.tableData[i];
        //     if (this.hasSpecialPriceError(row)) {
        //       // @ts-ignore
        //       this.$parent.$parent.$parent.saveFail();
        //       const errorMessage = this.getSpecialPriceErrorMessage(row);
        //       reject(new Error(errorMessage));
        //       return;
        //     }
        //   }
        // }

        // 保存时不校验特价申请说明必填项（保存并关闭时可以为空）
        let params: SpecialApplyPriceOverviewSaveRequestV2 = new SpecialApplyPriceOverviewSaveRequestV2();
        params.applyDescription = this.specialApplyDescription;
        params.matLines = this.setTableData();
        params.owner = this.baseEntity.id as string;
        params.version = this.baseEntity.version as number;
        params.outGoing = null;
        params.otherExpenses = this.otherExpenses;

        SpecialApplyBillApi.savePriceOverview(params)
          .then((res) => {
            // 保存成功后，更新父组件的特价申请说明校验状态
            const isValid = this.validateSpecialApplyDescription();
            if (
              this.$parent &&
              // @ts-ignore
              typeof this.$parent.updateSpecialApplyDescriptionValidStatus === 'function'
            ) {
              // @ts-ignore
              this.$parent.updateSpecialApplyDescriptionValidStatus(isValid);
            }
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    });
  }
  doSubmit() {
    // 校验表单
    // @ts-ignore
    this.$refs.form.validate((valid) => {
      if (!valid) {
        // @ts-ignore
        this.$parent.$parent.$parent.saveFail();
        this.$message.error('存在必填项未填写，请填写后再进入下一步');
        return Promise.reject(new Error('存在必填项未填写，请填写后再进入下一步'));
      }

      // 校验特价合法性（仅在有预估费用场景下）
      if (this.otherExpenses) {
        for (let i = 0; i < this.tableData.length; i++) {
          const row = this.tableData[i];
          if (this.hasSpecialPriceError(row)) {
            // @ts-ignore
            this.$parent.$parent.$parent.saveFail();
            const errorMessage = this.getSpecialPriceErrorMessage(row);
            this.$message.error(errorMessage);
            return Promise.reject(new Error(errorMessage));
          }
        }
      }

      // 校验特价申请说明必填项
      if (!this.validateSpecialApplyDescription()) {
        // @ts-ignore
        this.$parent.$parent.$parent.saveFail();
        this.$message.error('请填写特价申请说明');
        return Promise.reject(new Error('请填写特价申请说明'));
      }

      let params: SpecialApplyPriceOverviewSaveRequestV2 = new SpecialApplyPriceOverviewSaveRequestV2();
      params.applyDescription = this.specialApplyDescription;
      params.matLines = this.setTableData();
      params.owner = this.baseEntity.id as string;
      params.version = this.baseEntity.version as number;
      params.outGoing = null;
      params.otherExpenses = this.otherExpenses;
      if (this.baseEntity.userTaskOutGoings) {
        let findItem = this.baseEntity.userTaskOutGoings.find((item) => {
          return item.key === 'submit';
        });
        if (findItem) {
          params.outGoing = findItem;
        }
      }
      new Dialog(SpecialSubmitDialog, {
        params,
        method: this.submitMethod,
        sucess: () => {
          this.$router.push({
            name: RouterNames.specialApplyList,
          });
        },
        cancel: () => {
          // @ts-ignore
          this.$parent.goStep1();
        },
      }).show();
    });
  }
  submitMethod(params) {
    return SpecialApplyBillApi.saveAndsubmit(params);
  }

  formatSpecialPriceApplicableArea(row) {
    if (row && row.applicationScope) {
      let applicationScope = row.applicationScope;
      let content = `子公司：${applicationScope.sbsdyName}`;

      if (this.baseEntity.specialType != 'adjustZoneGuidePrice') {
        content += `\n办事处：${applicationScope.ofcName}`;
      }

      content += `\n销往国：${this.formatCtryName(applicationScope)}`;

      if (this.baseEntity.specialType == 'importantProjects') {
        content += `\n客户：${applicationScope.custName}`;
      }

      content += `\n币种：${applicationScope.currencyId}`;

      return content;
    }
    return '--';
  }

  // 格式化销往国
  formatCtryName(applicationScope) {
    if (!applicationScope.ctrys || applicationScope.ctrys.length <= 0) {
      return '--';
    }
    let res: string[] = [];
    for (let ctry of applicationScope.ctrys) {
      for (let applicationScopeCtryItem of ctry.applicationScopeCtry) {
        res.push(applicationScopeCtryItem.name as string);
      }
    }
    if (res.length <= 0) {
      return '--';
    }
    return res.join('、');
  }

  // 报价是否包含以下费用项
  handleOtherExpensesChange(value) {
    // 如果当前值和新值相同，直接返回
    if (this.otherExpenses === value) {
      return;
    }
    // 没有已填写的内容，清空并切换
    if (!this.hasFilledData()) {
      this.clearAllData();
      this.otherExpenses = value;
      return;
    }

    // 存在已填写的内容，弹窗确认
    this.$confirm(`此操作将清空您已配置的所有费用信息，您需要重新录入。<br/><br/>确定要继续吗？`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true, // 将 message 属性作为 HTML 片段处理，用于支持换行
    }).then(() => {
      // 先清空已填写的内容，再切换类型
      this.clearAllData();
      this.otherExpenses = value;
    });
  }

  // 是否存在已填写的内容
  hasFilledData() {
    if (!this.tableData || this.tableData.length === 0) {
      return false;
    }

    // 检查是否有已填写的数据
    return this.tableData.some((row) => {
      if (this.otherExpenses) {
        // 场景2：检查综合特价、预估费用等字段
        return row.total || row.logisticsExpenses || row.otherExpenses || (row.limitQty && row.qty);
      } else {
        // 场景1：检查调价差值、设备特价等字段
        return (
          row.differAmt !== null ||
          (row.specialPrice && row.specialPrice !== row.exwPrice) ||
          (row.limitQty && row.qty)
        );
      }
    });
  }

  // 清空所有已填写的内容
  clearAllData() {
    if (!this.tableData || this.tableData.length === 0) {
      return;
    }

    // 重置表格数据中的用户输入字段
    this.tableData.forEach((row, index) => {
      if (this.otherExpenses) {
        // 场景2：清空综合特价相关字段
        row.total = null;
        row.logisticsExpenses = null;
        row.otherExpenses = null;
        row.specialPrice = Number(row.exwPrice); // 重置为指导价
        row.differAmt = null;
        row.adjustRange = null;
      } else {
        // 场景1：重置为默认值
        row.specialPrice = Number(row.exwPrice);
        row.differAmt = null;
        row.adjustRange = null;
      }

      // 通用字段重置
      if (this.baseEntity.specialType === SpecialType.sampleMachine) {
        row.limitQty = true; // 样机测试必须限量
        row.qty = 1;
      } else {
        row.limitQty = false;
        row.qty = 999999;
      }

      // 更新响应式数据
      this.$set(this.tableData, index, { ...row });
    });

    // 如果有侧弹窗，也一并清除
    this.showFillDrawer = false;
    this.showCostEstimate = false;
  }

  // 从综合特价计算设备特价
  calculateSpecialPriceFromTotal(row: any): number {
    if (!row.total) {
      return 0;
    }

    const logisticsFee = row.logisticsExpenses || 0;
    const otherFee = row.otherExpenses || 0;

    return row.total - logisticsFee - otherFee;
  }

  // 检查特价是否有错误
  hasSpecialPriceError(row: any): boolean {
    if (!this.otherExpenses || !row.total) {
      return false;
    }

    const calculatedSpecialPrice = this.calculateSpecialPriceFromTotal(row);
    return calculatedSpecialPrice <= 0 || calculatedSpecialPrice > row.exwPrice;
  }

  // 格式化特价显示（包含错误信息）
  getSpecialPriceErrorMessage(row: any): string {
    const calculatedSpecialPrice = this.calculateSpecialPriceFromTotal(row);

    if (calculatedSpecialPrice <= 0) {
      return '特价不能≤0，请调整综合特价';
    }

    if (calculatedSpecialPrice > row.exwPrice) {
      return '特价不能高于指导价，请调整综合特价';
    }

    return '';
  }

  // 打开费用估算
  async openFeeEstimation() {
    // 检查是否有填写了综合特价的行
    const hasValidRows = this.tableData.some((row) => row.total);

    if (!hasValidRows) {
      this.$message.error('请先录入综合特价');
      return;
    }

    try {
      // 先保存当前数据
      await this.doSave();
      this.refreshBaseEntity();
      // 保存成功后打开费用估算弹窗
      this.showCostEstimate = true;
    } catch (error) {
      // 保存失败，不打开弹窗
      console.error('保存失败，无法打开费用估算:', error);
      this.$message.error((error as any).message || '保存失败');
    }
  }

  // 清空填写内容按钮
  handleClearFeeData() {
    this.$confirm(
      '此操作将清空您已配置的所有费用信息和已生成的预估费用， 您需要重新录入。<br/><br/>确定要继续吗？',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true, // 将 message 属性作为 HTML 片段处理，用于支持换行
      }
    ).then(() => {
      // 清空所有费用相关数据
      this.clearAllData();
      this.$message.success('已清空所有费用数据');
    });
  }

  // 刷新baseEntity
  refreshBaseEntity() {
    // @ts-ignore
    this.$parent.$parent.$parent.saveSuccess({ model: 'refresh', id: this.baseEntity.id });
    this.getData();
  }
}
