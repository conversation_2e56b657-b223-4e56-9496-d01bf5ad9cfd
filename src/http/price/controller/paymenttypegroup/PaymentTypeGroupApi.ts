import ApiClient from 'http/ApiClient'
import I18IdNameChecked from 'model/remote/support/core/domain/I18IdNameChecked'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import PaymentTypeGroupEditIn from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupEditIn'
import PaymentTypeGroupListIn from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupListIn'
import PaymentTypeGroupListOut from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupListOut'
import PaymentTypeGroupOpenApi from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupOpenApi'
import PaymentTypeGroupOut from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupOut'
import PaymentTypeGroupQueryPaymentIn from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupQueryPaymentIn'
import PaymentTypeGroupSaveIn from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupSaveIn'
import PaymentTypeGroupValidateIn from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupValidateIn'
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType'

export default class PaymentTypeGroupApi {
  /**
   * 新增
   * 
   */
  static create(body: PaymentTypeGroupSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/paymentTypeManage/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/paymentTypeManage/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static edit(body: PaymentTypeGroupEditIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/paymentTypeManage/edit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<PaymentTypeGroupOut>> {
    return ApiClient.server().get(`data/paymentTypeManage/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 付款方式 通过出口类型，列表查询全部
   * 
   */
  static listAllPaymentType(exportType: ExportType): Promise<LtcResponse<PaymentTypeGroupOpenApi[]>> {
    return ApiClient.server().get(`data/paymentTypeManage/listAllByExportType`, {
      params: {
        exportType: exportType
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: PaymentTypeGroupListIn): Promise<LtcPageResponse<PaymentTypeGroupListOut>> {
    return ApiClient.server().post(`data/paymentTypeManage/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询付款方式
   * 
   */
  static listQueryPaymentType(body: PaymentTypeGroupQueryPaymentIn): Promise<LtcPageResponse<I18IdNameChecked>> {
    return ApiClient.server().post(`data/paymentTypeManage/listPaymentType`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验分组名称中英文重复
   * 
   */
  static validateGroupName(body: PaymentTypeGroupValidateIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`data/paymentTypeManage/validateGroupName`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
