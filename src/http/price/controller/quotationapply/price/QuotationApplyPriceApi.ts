import ApiClient from 'http/ApiClient'
import BQuotationApplyCommission from 'model/remote/price/controller/quotationapply/price/BQuotationApplyCommission'
import BQuotationApplyDiscount from 'model/remote/price/controller/quotationapply/price/BQuotationApplyDiscount'
import BQuotationApplyDiscountRequest from 'model/remote/price/controller/quotationapply/price/BQuotationApplyDiscountRequest'
import BQuotationApplyPriceDiscountMatLine from 'model/remote/price/controller/quotationapply/price/BQuotationApplyPriceDiscountMatLine'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class QuotationApplyPriceApi {
  /**
   * 计算子公司佣金
   * 
   */
  static calCommission(id: string, sbsdyId: string): Promise<LtcResponse<BQuotationApplyCommission>> {
    return ApiClient.server().post(`/price/quotationApply/price/calCommission`, {}, {
      params: {
        id: id,
        sbsdyId: sbsdyId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 物料折扣率变更重新计算单条物料数据
   * 
   */
  static calMatOtherFee(body: BQuotationApplyPriceDiscountMatLine): Promise<LtcResponse<BQuotationApplyPriceDiscountMatLine>> {
    return ApiClient.server().post(`/price/quotationApply/price/calMatLineOtherFee`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询子公司佣金
   * 
   */
  static getCommission(id: string): Promise<LtcResponse<BQuotationApplyCommission>> {
    return ApiClient.server().get(`/price/quotationApply/price/getCommission`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询折扣
   * 
   */
  static getDiscount(id: string): Promise<LtcResponse<BQuotationApplyDiscount>> {
    return ApiClient.server().get(`/price/quotationApply/price/getDiscount`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询物料折扣率
   * 
   */
  static getMatDiscount(id: string): Promise<LtcResponse<BQuotationApplyDiscountRequest>> {
    return ApiClient.server().get(`/price/quotationApply/price/getMatDiscount`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存子公司佣金
   * 
   */
  static saveCommission(body: BQuotationApplyCommission): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/price/saveCommission`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存折扣
   * 
   */
  static saveDiscount(body: BQuotationApplyDiscount): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/price/saveDiscount`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存折扣率
   * 
   */
  static saveDiscountRate(body: BQuotationApplyDiscountRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/price/saveDiscountRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价总览-子公司佣金点击完成报价并提交
   * 
   */
  static submitCommission(body: BQuotationApplyCommission): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/price/quotationApply/price/submitCommission`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价总览-计算折扣点击完成报价并提交
   * 
   */
  static submitDiscount(body: BQuotationApplyDiscount): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/price/quotationApply/price/submitDiscount`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价总览-保存折扣率点击完成报价并提交
   * 
   */
  static submitDiscountRate(body: BQuotationApplyDiscountRequest): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/price/quotationApply/price/submitDiscountRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 转折扣申请
   * 
   */
  static transDiscount(id: string, version: number): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/price/transDiscount`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 转特价申请
   * 
   */
  static transSpecial(id: string, version: number): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/price/transSpecial`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

}
