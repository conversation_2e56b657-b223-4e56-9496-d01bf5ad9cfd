import ApiClient from 'http/ApiClient'
import BLogisticsCost from 'model/remote/price/controller/quotationapply/logisticscost/BLogisticsCost'
import BLogisticsInquiry from 'model/remote/price/controller/quotationapply/logisticsdesign/BLogisticsInquiry'
import BQuotationMissRule from 'model/remote/price/controller/quotationapply/logisticsdesign/BQuotationMissRule'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class LogisticsDesignApi {
  /**
   * 获取物流询价
   * 
   * @param id 报价申请单id
   */
  static getLogisticsInquiry(id: string): Promise<LtcResponse<BLogisticsInquiry>> {
    return ApiClient.server().get(`/price/logisticsDesign/getLogisticsInquiry`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取定制装运方案
   * 
   * @param id 报价申请单id
   */
  static getPackingPlan(id: string): Promise<LtcResponse<BLogisticsCost>> {
    return ApiClient.server().get(`/price/logisticsDesign/getPackingPlan`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存物流询价
   * 
   */
  static saveLogisticsInquiry(body: BLogisticsInquiry): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/logisticsDesign/saveLogisticsInquiry`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存定制装运方案
   * 
   */
  static savePackingPlan(body: BLogisticsCost): Promise<LtcResponse<BQuotationMissRule[]>> {
    return ApiClient.server().post(`/price/logisticsDesign/savePackingPlan`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
