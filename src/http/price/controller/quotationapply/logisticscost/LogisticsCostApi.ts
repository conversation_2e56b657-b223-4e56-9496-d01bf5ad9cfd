import ApiClient from 'http/ApiClient'
import BFeeDetail from 'model/remote/price/controller/quotationapply/logisticscost/BFeeDetail'
import BFeeType from 'model/remote/price/controller/quotationapply/logisticscost/BFeeType'
import BFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/BFreightCostAdjust'
import BMatFreightLand from 'model/remote/price/controller/quotationapply/logisticscost/BMatFreightLand'
import BPackPlan from 'model/remote/price/controller/quotationapply/logisticscost/BPackPlan'
import BProtectionScheme from 'model/remote/price/controller/quotationapply/logisticscost/BProtectionScheme'
import BProtectionSchemeV2 from 'model/remote/price/controller/quotationapply/logisticscost/BProtectionSchemeV2'
import BQuotationApplyOptionalItem from 'model/remote/price/controller/quotationapply/logisticscost/BQuotationApplyOptionalItem'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class LogisticsCostApi {
  /**
   * 查询物流费用项
   * 
   * @param id 报价申请单id
   */
  static getFeeTypes(id: string): Promise<LtcResponse<BFeeType[]>> {
    return ApiClient.server().get(`/price/logisticsCost/getFeeTypes`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取运费改价
   * 
   * @param id 报价申请单id
   */
  static getFreightCostAdjust(id: string): Promise<LtcResponse<BFreightCostAdjust>> {
    return ApiClient.server().get(`/price/logisticsCost/getFreightCostAdjust`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询物流费用明细
   * 
   * @param id 报价申请单id
   */
  static getLogisticsFees(id: string): Promise<LtcResponse<BFeeDetail>> {
    return ApiClient.server().get(`/price/logisticsCost/getLogisticsFees`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取装运方案
   * 
   * @param id 报价申请单id
   * @param calc 是否计算装运方案。默认不计算
   */
  static getLogisticsPackingPlan(id: string, calc?: boolean): Promise<LtcResponse<BPackPlan>> {
    return ApiClient.server().get(`/price/logisticsCost/getLogisticsPackingPlan`, {
      params: {
        id: id,
        calc: calc
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取报价物料
   * 
   * @param id 报价申请单id
   * @param calc 是否计算默认发货地。默认不计算
   */
  static getMatFreightLand(id: string, calc?: boolean): Promise<LtcResponse<BMatFreightLand>> {
    return ApiClient.server().get(`/price/logisticsCost/getMatFreightLand`, {
      params: {
        id: id,
        calc: calc
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取自选防护方案
   * 
   * @param id 报价申请单id
   */
  static getOptionalProtectionItem(id: string): Promise<LtcResponse<BQuotationApplyOptionalItem>> {
    return ApiClient.server().get(`/price/logisticsCost/getOptionalProtectionItem`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取防护方案
   * 
   * @param id 报价申请单id
   * @param calc 是否计算装运方案。默认不计算
   */
  static getProtectionScheme(id: string, calc?: boolean): Promise<LtcResponse<BProtectionScheme>> {
    return ApiClient.server().get(`/price/logisticsCost/getProtectionScheme`, {
      params: {
        id: id,
        calc: calc
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取防护方案-V2
   * 
   * @param id 报价申请单id
   * @param calc 是否计算装运方案。默认不计算
   */
  static getProtectionSchemeV2(id: string, calc?: boolean): Promise<LtcResponse<BProtectionSchemeV2>> {
    return ApiClient.server().get(`/price/logisticsCost/getProtectionSchemeV2`, {
      params: {
        id: id,
        calc: calc
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存运费改价
   * 
   */
  static saveFreightCostAdjust(body: BFreightCostAdjust): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/logisticsCost/saveFreightCostAdjust`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存装运方案
   * 
   */
  static saveLogisticsPackingPlan(body: BPackPlan): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/logisticsCost/saveLogisticsPackingPlan`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存报价物料
   * 
   */
  static saveMatFreightLand(body: BMatFreightLand): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/logisticsCost/saveMatFreightLand`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存自选防护项
   * 
   */
  static saveOptionalProtectionItem(body: BQuotationApplyOptionalItem): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/logisticsCost/saveOptionalProtectionItem`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存防护方案
   * 
   */
  static saveProtectionScheme(body: BProtectionScheme): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/logisticsCost/saveProtectionScheme`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存防护方案-V2
   * 
   */
  static saveProtectionSchemeV2(body: BProtectionSchemeV2): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/logisticsCost/saveProtectionSchemeV2`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
