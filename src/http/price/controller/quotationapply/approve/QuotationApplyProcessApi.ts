import ApiClient from 'http/ApiClient'
import BCreditInsuranceRateAndCountry from 'model/remote/price/controller/quotationapply/approve/BCreditInsuranceRateAndCountry'
import BProcessSaveCreditRateRequest from 'model/remote/price/controller/quotationapply/approve/BProcessSaveCreditRateRequest'
import BQuotationApplyProcessGetOptionalItem from 'model/remote/price/controller/quotationapply/approve/BQuotationApplyProcessGetOptionalItem'
import BQuotationApplyProcessSaveOptionalItem from 'model/remote/price/controller/quotationapply/approve/BQuotationApplyProcessSaveOptionalItem'
import BSaveCostPriceRequest from 'model/remote/price/controller/quotationapply/approve/BSaveCostPriceRequest'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class QuotationApplyProcessApi {
  /**
   * 根据单据id、客户id查询注册国家、付款方式费率明细及参考信保费率
   * 
   */
  static getCreditInsuranceRateAndCountry(id: string, custId: string): Promise<LtcResponse<BCreditInsuranceRateAndCountry>> {
    return ApiClient.server().get(`/price/quotationApply/approve/getCreditInsuranceRateAndCountry`, {
      params: {
        id: id,
        custId: custId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取填写自选防护费
   * 
   * @param id 报价申请单id
   */
  static getFillOptionalItemFee(id: string): Promise<LtcResponse<BQuotationApplyProcessGetOptionalItem>> {
    return ApiClient.server().get(`/price/quotationApply/approve/getFillOptionalItemFee`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 填写成本价
   * 
   */
  static saveCostPrice(body: BSaveCostPriceRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/approve/saveCostPrice`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审批保存特殊申请的信保费率
   * 
   */
  static saveCreditRate(body: BProcessSaveCreditRateRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/approve/saveCreditRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存填写自选防护费
   * 
   */
  static saveFillOptionalItemFee(body: BQuotationApplyProcessSaveOptionalItem): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/approve/saveFillOptionalItemFee`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
