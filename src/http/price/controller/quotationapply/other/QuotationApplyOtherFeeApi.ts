import ApiClient from 'http/ApiClient'
import BCalCreditForwardRateRequest from 'model/remote/price/controller/quotationapply/other/BCalCreditForwardRateRequest'
import BCalCreditForwardRateResponse from 'model/remote/price/controller/quotationapply/other/BCalCreditForwardRateResponse'
import BCalCreditInsuranceRateRequest from 'model/remote/price/controller/quotationapply/other/BCalCreditInsuranceRateRequest'
import BCalForwardDiscountRateRequest from 'model/remote/price/controller/quotationapply/other/BCalForwardDiscountRateRequest'
import BCalInterTransPremiumRateRequest from 'model/remote/price/controller/quotationapply/other/BCalInterTransPremiumRateRequest'
import BCalQuotationApplyOtherFeeRequest from 'model/remote/price/controller/quotationapply/other/BCalQuotationApplyOtherFeeRequest'
import BCalRateResponse from 'model/remote/price/controller/quotationapply/other/BCalRateResponse'
import BExtendedInfo from 'model/remote/price/controller/quotationapply/other/BExtendedInfo'
import BExtendedInfoFilter from 'model/remote/price/controller/quotationapply/other/BExtendedInfoFilter'
import BQuotationApplyFeeInfo from 'model/remote/price/controller/quotationapply/other/BQuotationApplyFeeInfo'
import BQuotationApplyOtherFee from 'model/remote/price/controller/quotationapply/other/BQuotationApplyOtherFee'
import BQuotationApplyOtherFeeMatRequest from 'model/remote/price/controller/quotationapply/other/BQuotationApplyOtherFeeMatRequest'
import BQuotationApplyPayInfo from 'model/remote/price/controller/quotationapply/other/BQuotationApplyPayInfo'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class QuotationApplyOtherFeeApi {
  /**
   * 计算远期贴现率和信保费率
   * 
   */
  static calCreditForwardRate(body: BCalCreditForwardRateRequest): Promise<LtcResponse<BCalCreditForwardRateResponse>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/calCreditForwardRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据客户、国家等信息计算信保费率
   * 
   */
  static calCreditInsuranceRate(body: BCalCreditInsuranceRateRequest): Promise<LtcResponse<BCalRateResponse>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/calCreditInsuranceRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算远期贴现率
   * 
   */
  static calForwardDiscountRate(body: BCalForwardDiscountRateRequest): Promise<LtcResponse<BCalRateResponse>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/calForwardDiscountRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 重算国际运输保险费费率
   * 
   */
  static calInterTransPremiumRate(body: BCalInterTransPremiumRateRequest): Promise<LtcResponse<number>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/calInterTransPremiumRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算其他费用,国际运输保险费设置变更时调用
   * 
   */
  static calOtherFee(body: BCalQuotationApplyOtherFeeRequest): Promise<LtcResponse<BQuotationApplyOtherFee>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/calOtherFee`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算合同总金额
   * 
   */
  static calQuotationApplyAmount(body: BQuotationApplyPayInfo): Promise<LtcResponse<number>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/calQuotationApplyAmount`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询已选择延保政策
   * 
   */
  static getDbExtendInfo(matId: string): Promise<LtcResponse<BExtendedInfo[]>> {
    return ApiClient.server().get(`/price/quotationApply/otherFee/getDbExtendInfo`, {
      params: {
        matId: matId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询延保政策
   * 
   */
  static getExtendInfo(body: BExtendedInfoFilter): Promise<LtcResponse<BExtendedInfo[]>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/getExtendInfo`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询报价物料信息
   * 
   */
  static getMatLine(id: string): Promise<LtcResponse<BQuotationApplyOtherFeeMatRequest>> {
    return ApiClient.server().get(`/price/quotationApply/otherFee/getMatLine`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询其他费用
   * 
   */
  static getOtherFee(id: string): Promise<LtcResponse<BQuotationApplyOtherFee>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/getOtherFee`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询其他费用名称
   * 
   * @param id 报价申请单id
   */
  static getOtherFeeName(id: string): Promise<LtcResponse<BQuotationApplyFeeInfo[]>> {
    return ApiClient.server().get(`/price/quotationApply/otherFee/getOtherFeeName`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询付款信息
   * 
   */
  static getPayInfo(id: string): Promise<LtcResponse<BQuotationApplyPayInfo>> {
    return ApiClient.server().get(`/price/quotationApply/otherFee/getPayInfo`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据国内运输保险费是否投保，重算其他费用
   * 
   */
  static reCalOtherFeeByDomesticInsurance(id: string, version: number, domesticInsurance: boolean): Promise<LtcResponse<BQuotationApplyOtherFee>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/reCalOtherFeeByDomesticInsurance`, {}, {
      params: {
        id: id,
        version: version,
        domesticInsurance: domesticInsurance
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存报价物料信息
   * 
   */
  static saveMatLine(body: BQuotationApplyOtherFeeMatRequest): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/saveMatLine`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存其他费用
   * 
   */
  static saveOtherFee(body: BQuotationApplyOtherFee): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/saveOtherFee`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存付款信息
   * 
   */
  static savePayInfo(body: BQuotationApplyPayInfo): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/price/quotationApply/otherFee/savePayInfo`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
