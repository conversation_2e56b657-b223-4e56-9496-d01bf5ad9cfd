import ApiClient from 'http/ApiClient'
import BCompleteQuotationApplyBillUserTaskRequest from 'model/remote/price/controller/quotationapply/bill/BCompleteQuotationApplyBillUserTaskRequest'
import BQuotationApplyBill from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBill'
import BQuotationApplyBillBatchCancelRequest from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBillBatchCancelRequest'
import BQuotationApplyBillFilter from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBillFilter'
import BQuotationApplyBillSubmitRequest from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBillSubmitRequest'
import BQuotationApplyMatRequest from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyMatRequest'
import BQuotationMissRule from 'model/remote/price/controller/quotationapply/logisticsdesign/BQuotationMissRule'
import BpfmHiTaskInstVO from 'model/remote/price/model/vo/bpfm/BpfmHiTaskInstVO'
import IdStringName from 'model/remote/support/core/domain/IdStringName'
import LtcPageResponse2 from 'model/remote/support/core/domain/LtcPageResponse2'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class QuotationApplyBillApi {
  /**
   * 作废
   * 仅针对已完成的作废
   * 
   */
  static abort(id: string, version: number): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/bill/abort`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 已废弃
   * 批量作废
   * 
   */
  static batchAbort(body: BQuotationApplyBillBatchCancelRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/bill/batchAbort`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 已废弃
   * 批量取消
   * 
   */
  static batchCancel(body: BQuotationApplyBillBatchCancelRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/bill/batchCancel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 取消
   * 
   */
  static cancel(id: string, version: number): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/bill/cancel`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 完成用户任务，处理【通过】【驳回】【添加意见】【无意见】等操作逻辑。回传查询返回的任务出口数据
   * 
   */
  static completeUserTask(body: BCompleteQuotationApplyBillUserTaskRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/bill/completeUserTask`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 复制单据
   * 复制单据
   * 
   */
  static copyBill(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/price/quotationApply/bill/copyBill`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询报价申请单基本信息
   * 
   */
  static get(id: string): Promise<LtcResponse<BQuotationApplyBill>> {
    return ApiClient.server().get(`/price/quotationApply/bill/get`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据流程ID查询报价申请单基本信息
   * 
   */
  static getByApproveProcessId(approveProcessId: string): Promise<LtcResponse<BQuotationApplyBill>> {
    return ApiClient.server().get(`/price/quotationApply/bill/getByApproveProcessId`, {
      params: {
        approveProcessId: approveProcessId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询报价申请单顶部信息
   * 
   */
  static getHeadInfo(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().get(`/price/quotationApply/bill/getHeadInfo`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据单号查询物料明细
   * 
   */
  static getMatLine(id: string): Promise<LtcResponse<BQuotationApplyMatRequest>> {
    return ApiClient.server().get(`/price/quotationApply/bill/getMatLine`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询报价申请单缺失的规则信息
   * 
   */
  static getMissRule(id: string): Promise<LtcResponse<BQuotationMissRule[]>> {
    return ApiClient.server().get(`/price/quotationApply/bill/getMissRule`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询历史任务
   * 
   */
  static listHistoryTask(id: string): Promise<LtcResponse<BpfmHiTaskInstVO[]>> {
    return ApiClient.server().get(`/price/quotationApply/bill/queryHistoryTask`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取产品线负责人负责的产品线
   * 
   */
  static listOwnerProductGroupId(id: string): Promise<LtcResponse<IdStringName[]>> {
    return ApiClient.server().get(`/price/quotationApply/bill/listOwnerProductGroupId`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询
   * 
   */
  static query(body: BQuotationApplyBillFilter): Promise<LtcPageResponse2<BQuotationApplyBill[]>> {
    return ApiClient.server().post(`/price/quotationApply/bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 
   */
  static save(body: BQuotationApplyBill): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/price/quotationApply/bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存物料明细
   * 
   */
  static saveMatLine(body: BQuotationApplyMatRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/bill/saveMatLine`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存关联合同
   * 
   */
  static saveRefContract(billNo: string, contractNo: string): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/quotationApply/bill/saveRefContract`, {}, {
      params: {
        billNo: billNo,
        contractNo: contractNo
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 提交审核
   * 
   */
  static submit(body: BQuotationApplyBillSubmitRequest): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/price/quotationApply/bill/submit`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
