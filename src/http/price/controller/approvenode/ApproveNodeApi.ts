import ApiClient from 'http/ApiClient'
import BBillApproveNodeDefinition from 'model/remote/price/controller/approvenode/BBillApproveNodeDefinition'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class ApproveNodeApi {
  /**
   * 查询某个单据的审批节点列表
   * 
   * @param billType 单据类型。ltc_quotation_apply_bill: 报价申请单。ltc_special_apply_bill: 特价申请单
   */
  static listDefinitions(billType: string): Promise<LtcResponse<BBillApproveNodeDefinition[]>> {
    return ApiClient.server().get(`/price/approveNode/listDefinitions`, {
      params: {
        billType: billType
      }
    }).then((res) => {
      return res.data
    })
  }

}
