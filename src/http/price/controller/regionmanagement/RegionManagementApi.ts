import ApiClient from 'http/ApiClient'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import NameCheckResultVo from 'model/remote/price/model/vo/regionmanagement/NameCheckResultVo'
import RegionCountryVo from 'model/remote/price/model/vo/regionmanagement/RegionCountryVo'
import RegionCtryQueryRequest from 'model/remote/price/model/vo/regionmanagement/RegionCtryQueryRequest'
import RegionManagementListIn from 'model/remote/price/model/vo/regionmanagement/RegionManagementListIn'
import RegionManagementListOut from 'model/remote/price/model/vo/regionmanagement/RegionManagementListOut'
import SaveRegionCtryRequest from 'model/remote/price/model/vo/regionmanagement/SaveRegionCtryRequest'
import SaveRegionCtrySortRequest from 'model/remote/price/model/vo/regionmanagement/SaveRegionCtrySortRequest'
import SaveRegionDetailRequest from 'model/remote/price/model/vo/regionmanagement/SaveRegionDetailRequest'
import SaveRegionRequest from 'model/remote/price/model/vo/regionmanagement/SaveRegionRequest'
import SortRegionCtryRequest from 'model/remote/price/model/vo/regionmanagement/SortRegionCtryRequest'
import SortRegionCtrySortRequest from 'model/remote/price/model/vo/regionmanagement/SortRegionCtrySortRequest'
import SortRegionDetailRequest from 'model/remote/price/model/vo/regionmanagement/SortRegionDetailRequest'
import SortRegionRequest from 'model/remote/price/model/vo/regionmanagement/SortRegionRequest'

export default class RegionManagementApi {
  /**
   * 检查国家分类是否已存在
   * 
   */
  static checkRegionCtrySortExists(body: SaveRegionCtrySortRequest): Promise<LtcResponse<NameCheckResultVo>> {
    return ApiClient.server().post(`data/regionManagement/checkRegionCtrySortExists`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检查细分区域是否已存在
   * 
   */
  static checkRegionDetailExists(body: SaveRegionDetailRequest): Promise<LtcResponse<NameCheckResultVo>> {
    return ApiClient.server().post(`data/regionManagement/checkRegionDetailExists`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 检查区域是否已存在
   * 
   */
  static checkRegionExists(body: SaveRegionRequest): Promise<LtcResponse<NameCheckResultVo>> {
    return ApiClient.server().post(`data/regionManagement/checkRegionExists`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除区域
   * 
   */
  static deleteRegion(code: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/regionManagement/deleteRegion`, {}, {
      params: {
        code: code,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除国家分类-国家
   * 
   */
  static deleteRegionCtry(code: string): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`data/regionManagement/deleteRegionCtry`, {}, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除国家分类
   * 
   */
  static deleteRegionCtrySort(code: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/regionManagement/deleteRegionCtrySort`, {}, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除细分区域
   * 
   */
  static deleteRegionDetail(code: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/regionManagement/deleteRegionDetail`, {}, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取大区/细分区域/国家分类流水号, prefix=null 获取大区流水号, prefix!=null, 获取细分区域/国家分类流水号,prefix是大区流水号/大区流水号+细分区域流水号
   * 
   */
  static getFlowCode(): Promise<LtcResponse<string>> {
    return ApiClient.server().get(`data/regionManagement/getFlowCode`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listAll(): Promise<LtcResponse<RegionManagementListOut[]>> {
    return ApiClient.server().get(`data/regionManagement/listAll`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询(按照大区分页，B端实际不需要使用此接口)
   * 
   */
  static listQuery(body: RegionManagementListIn): Promise<LtcPageResponse<RegionManagementListOut>> {
    return ApiClient.server().post(`data/regionManagement/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询用户数据权限范围内的区域国家树
   * 
   */
  static listWithDataPermission(): Promise<LtcResponse<RegionManagementListOut[]>> {
    return ApiClient.server().get(`data/regionManagement/listWithDataPermission`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询国家
   * 
   */
  static queryCountry(body: RegionCtryQueryRequest): Promise<LtcPageResponse<RegionCountryVo>> {
    return ApiClient.server().post(`data/regionManagement/queryCountry`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存区域
   * 
   */
  static saveRegion(body: SaveRegionRequest): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/regionManagement/saveRegion`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存国家分类-国家
   * 
   */
  static saveRegionCtry(body: SaveRegionCtryRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`data/regionManagement/saveRegionCtry`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存国家分类
   * 
   */
  static saveRegionCtrySort(body: SaveRegionCtrySortRequest): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/regionManagement/saveRegionCtrySort`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存细分区域
   * 
   */
  static saveRegionDetail(body: SaveRegionDetailRequest): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/regionManagement/saveRegionDetail`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 重新排序区域
   * 
   */
  static sortRegion(body: SortRegionRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`data/regionManagement/sortRegion`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 重新排序国家分类下的国家
   * 
   */
  static sortRegionCtry(body: SortRegionCtryRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`data/regionManagement/sortRegionCtry`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 重新排序国家分类
   * 
   */
  static sortRegionCtrySort(body: SortRegionCtrySortRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`data/regionManagement/sortRegionCtrySort`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 重新排序细分区域
   * 
   */
  static sortRegionDetail(body: SortRegionDetailRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`data/regionManagement/sortRegionDetail`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
