import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import PackingPlanGetOut from 'model/remote/price/model/vo/packingplan/PackingPlanGetOut'
import PackingPlanListIn from 'model/remote/price/model/vo/packingplan/PackingPlanListIn'
import PackingPlanListOut from 'model/remote/price/model/vo/packingplan/PackingPlanListOut'
import PackingPlanSaveIn from 'model/remote/price/model/vo/packingplan/PackingPlanSaveIn'

export default class PackingPlanApi {
  /**
   * 新建
   * 
   */
  static create(body: PackingPlanSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packingPlan/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packingPlan/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<PackingPlanGetOut>> {
    return ApiClient.server().get(`/data/packingPlan/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取柜型
   * 
   */
  static getCabinetType(): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/packingPlan/getCabinetType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/packingPlan/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: PackingPlanListIn): Promise<LtcPageResponse<PackingPlanListOut>> {
    return ApiClient.server().post(`/data/packingPlan/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static update(body: PackingPlanSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packingPlan/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验集装箱装运方式机型是否已存在
   * 
   */
  static validateProdModel(prodModelId: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packingPlan/validateProdModel`, {}, {
      params: {
        prodModelId: prodModelId
      }
    }).then((res) => {
      return res.data
    })
  }

}
