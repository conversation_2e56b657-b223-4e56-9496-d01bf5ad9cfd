import ApiClient from 'http/ApiClient'
import CcyExchangeRequest from 'model/remote/support/feign/obs/CcyExchangeRequest'
import CcyExchangeResult from 'model/remote/support/feign/obs/CcyExchangeResult'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class CcyExchangeApi {
  /**
   * 币种转换
   * 
   */
  static ccyExchange(body: CcyExchangeRequest): Promise<LtcResponse<CcyExchangeResult>> {
    return ApiClient.server().post(`/data/ccy/exchange`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
