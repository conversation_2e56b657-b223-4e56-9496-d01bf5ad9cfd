import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import WholeMachineCostPriceGetOut from 'model/remote/price/model/vo/wholemachinecostprice/WholeMachineCostPriceGetOut'
import WholeMachineCostPriceListIn from 'model/remote/price/model/vo/wholemachinecostprice/WholeMachineCostPriceListIn'
import WholeMachineCostPriceListOut from 'model/remote/price/model/vo/wholemachinecostprice/WholeMachineCostPriceListOut'
import WholeMachineCostPriceSaveIn from 'model/remote/price/model/vo/wholemachinecostprice/WholeMachineCostPriceSaveIn'

export default class WholeMachineCostPriceApi {
  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/wholeMachineCostPrice/deleteById`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: WholeMachineCostPriceListIn): Promise<void> {
    return ApiClient.server().post(`/data/wholeMachineCostPrice/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<WholeMachineCostPriceGetOut>> {
    return ApiClient.server().get(`/data/wholeMachineCostPrice/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/wholeMachineCostPrice/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新增
   * 
   */
  static list(body: WholeMachineCostPriceSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/wholeMachineCostPrice/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: WholeMachineCostPriceListIn): Promise<LtcPageResponse<WholeMachineCostPriceListOut>> {
    return ApiClient.server().post(`/data/wholeMachineCostPrice/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/wholeMachineCostPrice/switchEnabled`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

}
