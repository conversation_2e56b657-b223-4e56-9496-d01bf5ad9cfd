import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import RelateSettlePriceListIn from 'model/remote/price/model/vo/relatesettleprice/RelateSettlePriceListIn'
import RelateSettlePriceListOut from 'model/remote/price/model/vo/relatesettleprice/RelateSettlePriceListOut'
import RelateSettlePriceSaveIn from 'model/remote/price/model/vo/relatesettleprice/RelateSettlePriceSaveIn'

export default class RelateSettlePriceApi {
  /**
   * 新建
   * 
   */
  static create(body: RelateSettlePriceSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/relateSettlePrice/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/relateSettlePrice/deleteById`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: RelateSettlePriceListIn): Promise<void> {
    return ApiClient.server().post(`data/relateSettlePrice/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<RelateSettlePriceListOut>> {
    return ApiClient.server().get(`data/relateSettlePrice/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/relateSettlePrice/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: RelateSettlePriceListIn): Promise<LtcPageResponse<RelateSettlePriceListOut>> {
    return ApiClient.server().post(`data/relateSettlePrice/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/relateSettlePrice/switchEnabled`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

}
