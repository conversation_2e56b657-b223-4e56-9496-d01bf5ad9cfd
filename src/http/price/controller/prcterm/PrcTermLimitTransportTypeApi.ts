import ApiClient from 'http/ApiClient'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import PrcTermLimitTransportTypeListIn from 'model/remote/price/model/vo/prcterm/PrcTermLimitTransportTypeListIn'
import PrcTermLimitTransportTypeListOut from 'model/remote/price/model/vo/prcterm/PrcTermLimitTransportTypeListOut'
import TcDict from 'model/remote/data/model/vo/tcdict/TcDict'

export default class PrcTermLimitTransportTypeApi {
  /**
   * 查询指定贸易术语允许的运输模式数据字典
   * 
   */
  static getByArPrcTerm(arPrcTerm: string, lang: string): Promise<LtcResponse<TcDict[]>> {
    return ApiClient.server().get(`/data/prcTermLimitTransportType/getLimitDictsByArPrcTerm`, {
      params: {
        arPrcTerm: arPrcTerm,
        lang: lang
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: PrcTermLimitTransportTypeListIn): Promise<LtcPageResponse<PrcTermLimitTransportTypeListOut>> {
    return ApiClient.server().post(`/data/prcTermLimitTransportType/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
