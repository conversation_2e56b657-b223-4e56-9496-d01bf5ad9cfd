import ApiClient from 'http/ApiClient'
import DestinationPortListIn from 'model/remote/price/model/vo/destinationport/DestinationPortListIn'
import DestinationPortListOut from 'model/remote/price/model/vo/destinationport/DestinationPortListOut'
import DestinationPortSaveIn from 'model/remote/price/model/vo/destinationport/DestinationPortSaveIn'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class DestinationPortApi {
  /**
   * 新建目的港/目的站资料
   * 
   */
  static create(body: DestinationPortSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/destinationPort/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑目的港/目的站资料
   * 
   */
  static edit(body: DestinationPortSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/destinationPort/edit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: DestinationPortListIn): Promise<LtcPageResponse<DestinationPortListOut>> {
    return ApiClient.server().post(`/data/destinationPort/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询目的港/目的站是否存在类似数据
   * 
   * @param nameEn 英文名称
   */
  static querySimilarName(nameEn: string): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/destinationPort/querySimilarName`, {
      params: {
        nameEn: nameEn
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 转换数据状态
   * 
   * @param code 编码
   */
  static switchEnabled(code: string): Promise<LtcResponse<number>> {
    return ApiClient.server().post(`/data/destinationPort/switchEnabled`, {}, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验目的港/目的站是否存在相同数据
   * 
   * @param nameEn 英文名称
   */
  static verifySameName(nameEn: string): Promise<LtcResponse<boolean>> {
    return ApiClient.server().get(`/data/destinationPort/verifySameName`, {
      params: {
        nameEn: nameEn
      }
    }).then((res) => {
      return res.data
    })
  }

}
