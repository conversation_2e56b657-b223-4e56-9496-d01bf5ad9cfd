import ApiClient from 'http/ApiClient'
import DomesticFreightPortIn from 'model/remote/price/model/vo/domesticfreight/DomesticFreightPortIn'
import DomesticFreightPortOut from 'model/remote/price/model/vo/domesticfreight/DomesticFreightPortOut'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class DomesticFreightPortApi {
  /**
   * 新建起运港/起运站资料
   * 
   */
  static create(body: DomesticFreightPortIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/domesticFreightPort/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑起运港/起运站资料
   * 
   */
  static edit(body: DomesticFreightPortIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/domesticFreightPort/edit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: DomesticFreightPortIn): Promise<LtcPageResponse<DomesticFreightPortOut>> {
    return ApiClient.server().post(`/data/domesticFreightPort/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询起运港/起运站是否存在类似数据
   * 
   * @param name 名称
   */
  static querySimilarName(name: string): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/domesticFreightPort/querySimilarName`, {
      params: {
        name: name
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 转换数据状态
   * 
   * @param code 代码
   */
  static switchEnabled(code: string): Promise<LtcResponse<number>> {
    return ApiClient.server().post(`/data/domesticFreightPort/switchEnabled`, {}, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验起运港/起运站是否存在相同数据
   * 
   * @param name 名称
   */
  static verifySameName(name: string): Promise<LtcResponse<boolean>> {
    return ApiClient.server().get(`/data/domesticFreightPort/verifySameName`, {
      params: {
        name: name
      }
    }).then((res) => {
      return res.data
    })
  }

}
