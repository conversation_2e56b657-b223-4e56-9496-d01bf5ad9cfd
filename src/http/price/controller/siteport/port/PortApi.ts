import ApiClient from 'http/ApiClient'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import PortListOut from 'model/remote/price/model/vo/userusedport/PortListOut'

export default class PortApi {
  /**
   * 删除用户使用过的站点
   * 
   * @param portType 站点类型
   * @param code 站点代码
   */
  static deleteUserUsedPort(portType: string, code: string): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/port/deleteUserUsedPort`, {}, {
      params: {
        portType: portType,
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   * @param portType 站点类型: domesticFreightPort：起运港/起运站，destinationPort：目的港/目的站
   */
  static getUsablePorts(portType: string): Promise<LtcResponse<PortListOut>> {
    return ApiClient.server().get(`/data/port/getUsablePorts`, {
      params: {
        portType: portType
      }
    }).then((res) => {
      return res.data
    })
  }

}
