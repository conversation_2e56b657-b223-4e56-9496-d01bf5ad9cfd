import ApiClient from 'http/ApiClient'
import DomesticFreightLandIn from 'model/remote/price/model/vo/domesticfreight/DomesticFreightLandIn'
import DomesticFreightLandOut from 'model/remote/price/model/vo/domesticfreight/DomesticFreightLandOut'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class DomesticFreightLandApi {
  /**
   * 新建发货地/出发地资料
   * 
   */
  static create(body: DomesticFreightLandIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/domesticFreightLand/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑发货地/出发地资料
   * 
   */
  static edit(body: DomesticFreightLandIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/domesticFreightLand/edit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: DomesticFreightLandIn): Promise<LtcPageResponse<DomesticFreightLandOut>> {
    return ApiClient.server().post(`/data/domesticFreightLand/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询发货地/出发地是否存在类似数据
   * 
   * @param name 名称
   */
  static querySimilarName(name: string): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/domesticFreightLand/querySimilarName`, {
      params: {
        name: name
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 转换数据状态
   * 
   * @param code 代码
   */
  static switchEnabled(code: string): Promise<LtcResponse<number>> {
    return ApiClient.server().post(`/data/domesticFreightLand/switchEnabled`, {}, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验发货地/出发地是否存在相同数据
   * 
   * @param name 名称
   */
  static verifySameName(name: string): Promise<LtcResponse<boolean>> {
    return ApiClient.server().get(`/data/domesticFreightLand/verifySameName`, {
      params: {
        name: name
      }
    }).then((res) => {
      return res.data
    })
  }

}
