import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'
import TrainFreightRuleListIn from 'model/remote/price/model/vo/trainfreightrule/TrainFreightRuleListIn'
import TrainFreightRuleListOut from 'model/remote/price/model/vo/trainfreightrule/TrainFreightRuleListOut'
import TrainFreightRuleRatioSetIn from 'model/remote/price/model/vo/trainfreightrule/TrainFreightRuleRatioSetIn'
import TrainFreightRuleSaveIn from 'model/remote/price/model/vo/trainfreightrule/TrainFreightRuleSaveIn'

export default class TrainFreightRuleApi {
  /**
   * 新建
   * 
   */
  static create(body: TrainFreightRuleSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/trainFreightRule/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/trainFreightRule/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: TrainFreightRuleListIn): Promise<void> {
    return ApiClient.server().post(`/data/trainFreightRule/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取柜型
   * 
   */
  static getCabinetType(): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/trainFreightRule/getCabinetType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/trainFreightRule/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/trainFreightRule/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: TrainFreightRuleListIn): Promise<LtcPageResponse<TrainFreightRuleListOut>> {
    return ApiClient.server().post(`/data/trainFreightRule/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: TrainFreightRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/trainFreightRule/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
