import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import OceanLooseFreightRuleListIn from 'model/remote/price/model/vo/oceanloosefreightrule/OceanLooseFreightRuleListIn'
import OceanLooseFreightRuleListOut from 'model/remote/price/model/vo/oceanloosefreightrule/OceanLooseFreightRuleListOut'
import OceanLooseFreightRuleRatioSetIn from 'model/remote/price/model/vo/oceanloosefreightrule/OceanLooseFreightRuleRatioSetIn'
import OceanLooseFreightRuleSaveIn from 'model/remote/price/model/vo/oceanloosefreightrule/OceanLooseFreightRuleSaveIn'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'

export default class OceanLooseFreightRuleApi {
  /**
   * 新建
   * 
   */
  static create(body: OceanLooseFreightRuleSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanLooseFreightRule/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanLooseFreightRule/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 海运滚装散杂运费单价导出
   * 
   */
  static export(body: OceanLooseFreightRuleListIn): Promise<void> {
    return ApiClient.server().post(`/data/oceanLooseFreightRule/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/oceanLooseFreightRule/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/oceanLooseFreightRule/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: OceanLooseFreightRuleListIn): Promise<LtcPageResponse<OceanLooseFreightRuleListOut>> {
    return ApiClient.server().post(`/data/oceanLooseFreightRule/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: OceanLooseFreightRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/oceanLooseFreightRule/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
