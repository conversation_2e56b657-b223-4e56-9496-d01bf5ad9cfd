import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import OceanAndTrainFreightRuleListIn from 'model/remote/price/model/vo/oceanandtrainfreightrule/OceanAndTrainFreightRuleListIn'
import OceanAndTrainFreightRuleListOut from 'model/remote/price/model/vo/oceanandtrainfreightrule/OceanAndTrainFreightRuleListOut'
import OceanAndTrainFreightRuleRatioSetIn from 'model/remote/price/model/vo/oceanandtrainfreightrule/OceanAndTrainFreightRuleRatioSetIn'
import OceanAndTrainFreightRuleSaveIn from 'model/remote/price/model/vo/oceanandtrainfreightrule/OceanAndTrainFreightRuleSaveIn'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'

export default class OceanAndTrainFreightRuleApi {
  /**
   * 新建
   * 
   */
  static create(body: OceanAndTrainFreightRuleSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanAndTrainFreightRule/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanAndTrainFreightRule/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 海运滚装散杂运费单价导出
   * 
   */
  static export(body: OceanAndTrainFreightRuleListIn): Promise<void> {
    return ApiClient.server().post(`/data/oceanAndTrainFreightRule/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取柜型
   * 
   */
  static getCabinetType(): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/oceanAndTrainFreightRule/getCabinetType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/oceanAndTrainFreightRule/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/oceanAndTrainFreightRule/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: OceanAndTrainFreightRuleListIn): Promise<LtcPageResponse<OceanAndTrainFreightRuleListOut>> {
    return ApiClient.server().post(`/data/oceanAndTrainFreightRule/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: OceanAndTrainFreightRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/oceanAndTrainFreightRule/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
