import ApiClient from 'http/ApiClient'
import ForwardDiscountRateGetOut from 'model/remote/price/model/vo/feerate/forwarddiscountrate/ForwardDiscountRateGetOut'
import ForwardDiscountRateListIn from 'model/remote/price/model/vo/feerate/forwarddiscountrate/ForwardDiscountRateListIn'
import ForwardDiscountRateListOut from 'model/remote/price/model/vo/feerate/forwarddiscountrate/ForwardDiscountRateListOut'
import ForwardDiscountRateSaveIn from 'model/remote/price/model/vo/feerate/forwarddiscountrate/ForwardDiscountRateSaveIn'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class ForwardDiscountRateApi {
  /**
   * 新建
   * 
   */
  static create(body: ForwardDiscountRateSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/forwardDiscountRate/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/forwardDiscountRate/deleteById`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<ForwardDiscountRateGetOut>> {
    return ApiClient.server().get(`data/forwardDiscountRate/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/forwardDiscountRate/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: ForwardDiscountRateListIn): Promise<LtcPageResponse<ForwardDiscountRateListOut>> {
    return ApiClient.server().post(`data/forwardDiscountRate/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/forwardDiscountRate/switchEnabled`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static update(body: ForwardDiscountRateSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/forwardDiscountRate/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
