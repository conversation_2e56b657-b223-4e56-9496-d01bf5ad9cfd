import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import InternationalTransportInsuranceRateDetailVo from 'model/remote/price/model/vo/feerate/internationaltransportinsurancerate/InternationalTransportInsuranceRateDetailVo'
import InternationalTransportInsuranceRateEditIn from 'model/remote/price/model/vo/feerate/internationaltransportinsurancerate/InternationalTransportInsuranceRateEditIn'
import InternationalTransportInsuranceRateGetOut from 'model/remote/price/model/vo/feerate/internationaltransportinsurancerate/InternationalTransportInsuranceRateGetOut'
import InternationalTransportInsuranceRateListIn from 'model/remote/price/model/vo/feerate/internationaltransportinsurancerate/InternationalTransportInsuranceRateListIn'
import InternationalTransportInsuranceRateListOut from 'model/remote/price/model/vo/feerate/internationaltransportinsurancerate/InternationalTransportInsuranceRateListOut'
import InternationalTransportInsuranceRateSaveIn from 'model/remote/price/model/vo/feerate/internationaltransportinsurancerate/InternationalTransportInsuranceRateSaveIn'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class InternationalTransportInsuranceRateApi {
  /**
   * 新建
   * 
   */
  static create(body: InternationalTransportInsuranceRateSaveIn): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`data/internationalTransportInsuranceRate/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/internationalTransportInsuranceRate/deleteById`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<InternationalTransportInsuranceRateGetOut>> {
    return ApiClient.server().get(`data/internationalTransportInsuranceRate/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取运输方式
   * 
   */
  static getTransportType(): Promise<LtcResponse<InternationalTransportInsuranceRateDetailVo[]>> {
    return ApiClient.server().get(`data/internationalTransportInsuranceRate/getTransportType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/internationalTransportInsuranceRate/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: InternationalTransportInsuranceRateListIn): Promise<LtcPageResponse<InternationalTransportInsuranceRateListOut>> {
    return ApiClient.server().post(`data/internationalTransportInsuranceRate/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/internationalTransportInsuranceRate/switchEnabled`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static update(body: InternationalTransportInsuranceRateEditIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/internationalTransportInsuranceRate/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
