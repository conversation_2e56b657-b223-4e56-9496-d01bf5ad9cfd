import ApiClient from 'http/ApiClient'
import CreditInsuranceRateListIn from 'model/remote/price/model/vo/feerate/creditinsurancerate/CreditInsuranceRateListIn'
import CreditInsuranceRateListOut from 'model/remote/price/model/vo/feerate/creditinsurancerate/CreditInsuranceRateListOut'
import CreditInsuranceRateSaveIn from 'model/remote/price/model/vo/feerate/creditinsurancerate/CreditInsuranceRateSaveIn'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class CreditInsuranceRateApi {
  /**
   * 新建
   * 
   */
  static create(body: CreditInsuranceRateSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/creditInsuranceRate/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/creditInsuranceRate/deleteById`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/creditInsuranceRate/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: CreditInsuranceRateListIn): Promise<LtcPageResponse<CreditInsuranceRateListOut>> {
    return ApiClient.server().post(`data/creditInsuranceRate/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/creditInsuranceRate/switchEnabled`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

}
