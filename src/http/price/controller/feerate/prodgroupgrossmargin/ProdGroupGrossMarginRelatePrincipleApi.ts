import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import ProdGroupGrossMarginRelatePrincipleListIn from 'model/remote/price/model/vo/feerate/prodgroupgrossmargin/ProdGroupGrossMarginRelatePrincipleListIn'
import ProdGroupGrossMarginRelatePrincipleListOut from 'model/remote/price/model/vo/feerate/prodgroupgrossmargin/ProdGroupGrossMarginRelatePrincipleListOut'
import ProdGroupGrossMarginRelatePrincipleSaveIn from 'model/remote/price/model/vo/feerate/prodgroupgrossmargin/ProdGroupGrossMarginRelatePrincipleSaveIn'

export default class ProdGroupGrossMarginRelatePrincipleApi {
  /**
   * 新建
   * 
   */
  static create(body: ProdGroupGrossMarginRelatePrincipleSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/prodGroupGrossMarginRelatePrinciple/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/prodGroupGrossMarginRelatePrinciple/deleteById`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: ProdGroupGrossMarginRelatePrincipleListIn): Promise<void> {
    return ApiClient.server().post(`data/prodGroupGrossMarginRelatePrinciple/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<ProdGroupGrossMarginRelatePrincipleListOut>> {
    return ApiClient.server().get(`data/prodGroupGrossMarginRelatePrinciple/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/prodGroupGrossMarginRelatePrinciple/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: ProdGroupGrossMarginRelatePrincipleListIn): Promise<LtcPageResponse<ProdGroupGrossMarginRelatePrincipleListOut>> {
    return ApiClient.server().post(`data/prodGroupGrossMarginRelatePrinciple/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/prodGroupGrossMarginRelatePrinciple/switchEnabled`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

}
