import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import SubsidiaryCommissionRateListIn from 'model/remote/price/model/vo/subsidiarycommissionrate/SubsidiaryCommissionRateListIn'
import SubsidiaryCommissionRateListOut from 'model/remote/price/model/vo/subsidiarycommissionrate/SubsidiaryCommissionRateListOut'
import SubsidiaryCommissionRateSaveIn from 'model/remote/price/model/vo/subsidiarycommissionrate/SubsidiaryCommissionRateSaveIn'
import UcnEnVo from 'model/remote/support/core/domain/UcnEnVo'
import UcnVo from 'model/remote/support/core/domain/UcnVo'

export default class SubsidiaryCommissionRateApi {
  /**
   * 新建
   * 
   */
  static create(body: SubsidiaryCommissionRateSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/subsidiaryCommissionRate/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/subsidiaryCommissionRate/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取出口类型
   * 
   */
  static getExportType(): Promise<LtcResponse<UcnVo[]>> {
    return ApiClient.server().get(`data/subsidiaryCommissionRate/getExportType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取常用固定产品线
   * 
   */
  static getFixedProdGroup(): Promise<LtcResponse<UcnEnVo[]>> {
    return ApiClient.server().get(`data/subsidiaryCommissionRate/getFixedProdGroup`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取销售模式
   * 
   */
  static getSource(): Promise<LtcResponse<UcnVo[]>> {
    return ApiClient.server().get(`data/subsidiaryCommissionRate/getSaleMode`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入子公司佣金率
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/subsidiaryCommissionRate/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: SubsidiaryCommissionRateListIn): Promise<LtcPageResponse<SubsidiaryCommissionRateListOut>> {
    return ApiClient.server().post(`data/subsidiaryCommissionRate/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
