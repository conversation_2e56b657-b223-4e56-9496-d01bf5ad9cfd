import ApiClient from 'http/ApiClient'
import ContainerProdModuleDomesticFreightListIn from 'model/remote/price/model/vo/domesticfreightprice/containerprodmodule/ContainerProdModuleDomesticFreightListIn'
import ContainerProdModuleDomesticFreightListOut from 'model/remote/price/model/vo/domesticfreightprice/containerprodmodule/ContainerProdModuleDomesticFreightListOut'
import ContainerProdModuleDomesticFreightSaveIn from 'model/remote/price/model/vo/domesticfreightprice/containerprodmodule/ContainerProdModuleDomesticFreightSaveIn'
import ContainerProdModuleDomesticFreightVerifyEffectiveTimeOut from 'model/remote/price/model/vo/domesticfreightprice/containerprodmodule/ContainerProdModuleDomesticFreightVerifyEffectiveTimeOut'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class ContainerProdModuleDomesticFreightApi {
  /**
   * 新建
   * 
   */
  static create(body: ContainerProdModuleDomesticFreightSaveIn, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/containerProdModuleDomesticFreight/create`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/containerProdModuleDomesticFreight/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: ContainerProdModuleDomesticFreightListIn): Promise<void> {
    return ApiClient.server().post(`/data/containerProdModuleDomesticFreight/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: ContainerProdModuleDomesticFreightListIn): Promise<LtcPageResponse<ContainerProdModuleDomesticFreightListOut>> {
    return ApiClient.server().post(`/data/containerProdModuleDomesticFreight/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/containerProdModuleDomesticFreight/switchEnabled`, {}, {
      params: {
        id: id,
        version: version,
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建-验证有效期
   * 
   */
  static verifyEffectiveTime(body: ContainerProdModuleDomesticFreightSaveIn): Promise<LtcResponse<ContainerProdModuleDomesticFreightVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`/data/containerProdModuleDomesticFreight/verifyEffectiveTime`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 启用-验证有效期
   * 
   */
  static verifyListEffectiveTime(id: string): Promise<LtcResponse<ContainerProdModuleDomesticFreightVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`/data/containerProdModuleDomesticFreight/verifyListEffectiveTime`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

}
