import ApiClient from 'http/ApiClient'
import DomesticFreightPriceRatioSetIn from 'model/remote/price/model/vo/domesticfreightprice/DomesticFreightPriceRatioSetIn'
import DomesticFreightRuleGetByIdOut from 'model/remote/price/model/vo/domesticfreightprice/truck/DomesticFreightRuleGetByIdOut'
import DomesticFreightRuleListIn from 'model/remote/price/model/vo/domesticfreightprice/truck/DomesticFreightRuleListIn'
import DomesticFreightRuleListOut from 'model/remote/price/model/vo/domesticfreightprice/truck/DomesticFreightRuleListOut'
import DomesticFreightRuleSaveIn from 'model/remote/price/model/vo/domesticfreightprice/truck/DomesticFreightRuleSaveIn'
import DomesticFreightRuleValidateOut from 'model/remote/price/model/vo/domesticfreightprice/truck/DomesticFreightRuleValidateOut'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'
import UcnVo from 'model/remote/support/core/domain/UcnVo'

export default class DomesticFreightRuleApi {
  /**
   * 新建
   * 
   */
  static create(body: DomesticFreightRuleSaveIn, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/domesticFreightRule/create`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/domesticFreightRule/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: DomesticFreightRuleListIn): Promise<void> {
    return ApiClient.server().post(`/data/domesticFreightRule/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<DomesticFreightRuleGetByIdOut>> {
    return ApiClient.server().get(`/data/domesticFreightRule/get`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/domesticFreightRule/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取创建方式
   * 
   */
  static getSource(): Promise<LtcResponse<UcnVo[]>> {
    return ApiClient.server().get(`/data/domesticFreightRule/getSource`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/domesticFreightRule/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: DomesticFreightRuleListIn): Promise<LtcPageResponse<DomesticFreightRuleListOut>> {
    return ApiClient.server().post(`/data/domesticFreightRule/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 重置businessKey
   * 
   */
  static resetBusinessKey(): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/domesticFreightRule/resetBusinessKey`, {}, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: DomesticFreightPriceRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/domesticFreightRule/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改
   * 
   */
  static update(body: DomesticFreightRuleSaveIn, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/domesticFreightRule/update`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 验证有效期-编辑
   * 
   */
  static verifyEffectiveTime(body: DomesticFreightRuleSaveIn): Promise<LtcResponse<DomesticFreightRuleValidateOut[]>> {
    return ApiClient.server().post(`/data/domesticFreightRule/verifyEffectiveTime`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
