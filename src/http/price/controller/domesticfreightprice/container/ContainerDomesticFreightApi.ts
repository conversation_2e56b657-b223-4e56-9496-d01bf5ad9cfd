import ApiClient from 'http/ApiClient'
import ContainerDomesticFreightListIn from 'model/remote/price/model/vo/domesticfreightprice/container/ContainerDomesticFreightListIn'
import ContainerDomesticFreightListOut from 'model/remote/price/model/vo/domesticfreightprice/container/ContainerDomesticFreightListOut'
import ContainerDomesticFreightSaveIn from 'model/remote/price/model/vo/domesticfreightprice/container/ContainerDomesticFreightSaveIn'
import DomesticFreightPriceRatioSetIn from 'model/remote/price/model/vo/domesticfreightprice/DomesticFreightPriceRatioSetIn'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'

export default class ContainerDomesticFreightApi {
  /**
   * 新建
   * 
   */
  static create(body: ContainerDomesticFreightSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/containerDomesticFreight/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/containerDomesticFreight/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: ContainerDomesticFreightListIn): Promise<void> {
    return ApiClient.server().post(`/data/containerDomesticFreight/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取柜型
   * 
   */
  static getCabinetType(): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/containerDomesticFreight/getCabinetType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/containerDomesticFreight/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/containerDomesticFreight/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: ContainerDomesticFreightListIn): Promise<LtcPageResponse<ContainerDomesticFreightListOut>> {
    return ApiClient.server().post(`/data/containerDomesticFreight/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: DomesticFreightPriceRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/containerDomesticFreight/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
