import ApiClient from 'http/ApiClient'
import DiscountLimitAdjustIn from 'model/remote/price/model/vo/discountlimit/DiscountLimitAdjustIn'
import DiscountLimitListIn from 'model/remote/price/model/vo/discountlimit/DiscountLimitListIn'
import DiscountLimitListOut from 'model/remote/price/model/vo/discountlimit/DiscountLimitListOut'
import DiscountLimitSaveIn from 'model/remote/price/model/vo/discountlimit/DiscountLimitSaveIn'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class DiscountLimitApi {
  /**
   * 额度调整
   * 
   */
  static adjustLimit(body: DiscountLimitAdjustIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/discountLimit/adjustLimit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建
   * 
   */
  static create(body: DiscountLimitSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/discountLimit/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: DiscountLimitListIn): Promise<void> {
    return ApiClient.server().post(`/data/discountLimit/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/discountLimit/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: DiscountLimitListIn): Promise<LtcPageResponse<DiscountLimitListOut>> {
    return ApiClient.server().post(`/data/discountLimit/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
