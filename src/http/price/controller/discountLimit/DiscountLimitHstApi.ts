import ApiClient from 'http/ApiClient'
import DiscountLimitHst from 'model/remote/price/api/discountlimit/DiscountLimitHst'
import DiscountLimitHstFilter from 'model/remote/price/api/discountlimit/DiscountLimitHstFilter'
import LtcPageResponse2 from 'model/remote/support/core/domain/LtcPageResponse2'

export default class DiscountLimitHstApi {
  /**
   * 导出
   * 
   */
  static export(body: DiscountLimitHstFilter): Promise<void> {
    return ApiClient.server().post(`/data/discountLimithst/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询
   * 
   */
  static query(body: DiscountLimitHstFilter): Promise<LtcPageResponse2<DiscountLimitHst[]>> {
    return ApiClient.server().post(`/data/discountLimithst/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
