import ApiClient from 'http/ApiClient'
import BillDiscountLimit from 'model/remote/price/api/discountlimit/BillDiscountLimit'
import BillDiscountLimitFilter from 'model/remote/price/api/discountlimit/BillDiscountLimitFilter'
import LtcPageResponse2 from 'model/remote/support/core/domain/LtcPageResponse2'

export default class BillDiscountLimitApi {
  /**
   * 导出
   * 
   */
  static export(body: BillDiscountLimitFilter): Promise<void> {
    return ApiClient.server().post(`/data/billdiscountLimit/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询
   * 
   */
  static query(body: BillDiscountLimitFilter): Promise<LtcPageResponse2<BillDiscountLimit[]>> {
    return ApiClient.server().post(`/data/billdiscountLimit/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
