import ApiClient from 'http/ApiClient'
import BQuotationApplyGenPdfRequest from 'model/remote/price/controller/genpdf/BQuotationApplyGenPdfRequest'
import BQuotationApplyHomeDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyHomeDataModel'
import BQuotationApplyOptionDetailsDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyOptionDetailsDataModel'
import BQuotationApplyOtherFeeMaterialDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyOtherFeeMaterialDataModel'
import BQuotationApplyOtherFeeSummaryDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyOtherFeeSummaryDataModel'
import BQuotationApplyTotalPriceMaterialDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyTotalPriceMaterialDataModel'
import BQuotationApplyTotalPriceSummaryDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyTotalPriceSummaryDataModel'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import UploadResult from 'model/remote/support/core/file/UploadResult'

export default class GenPdfApi {
  /**
   * 报价申请下载pdf。输入报价申请导出信息;输出生成pdf的oss路径
   * 
   */
  static downloadQuotationApply(body: BQuotationApplyGenPdfRequest): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/quotationApply/download`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价申请预览pdf。输入报价申请导出信息;输出生成pdf的oss路径
   * 
   */
  static previewQuotationApply(body: BQuotationApplyGenPdfRequest): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/quotationApply/preview`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 测试生成pdf,输入模板id及输入模板变量;输出生成pdf的oss路径
   * 
   */
  static test(body: any, templeteId: string): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/test`, body, {
      params: {
        templeteId: templeteId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 测试报价申请首页生成pdf,模板id固定为quotationApplyHome（前端需要在数据库维护该模板）。输入变量内容;输出生成pdf的oss路径
   * 
   */
  static testHome(body: BQuotationApplyHomeDataModel): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/test/quotationApplyHome`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 测试报价申请配置明细页/配置明细页(含价格)生成pdf（前端需要在数据库维护该模板）。输入变量内容;输出生成pdf的oss路径
   * 
   * @param price 是否含价格，false-不含价格,模板id固定为optionDetails;true-含价格,模板id固定为optionDetailsPrice
   */
  static testOptionDetails(body: BQuotationApplyOptionDetailsDataModel, price?: boolean): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/test/optionDetails`, body, {
      params: {
        price: price
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 测试报价申请其他费用物料页生成pdf,模板id固定为otherFeeMaterial（前端需要在数据库维护该模板）。输入变量内容;输出生成pdf的oss路径
   * 
   */
  static testOtherFeeMaterial(body: BQuotationApplyOtherFeeMaterialDataModel): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/test/otherFeeMaterial`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 测试报价申请其他费用Summary页生成pdf,模板id固定为otherFeeSummary（前端需要在数据库维护该模板）。输入变量内容;输出生成pdf的oss路径
   * 
   */
  static testOtherFeeSummary(body: BQuotationApplyOtherFeeSummaryDataModel): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/test/otherFeeSummary`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 测试报价申请总价页物料页生成pdf,模板id固定为totalPriceMaterial（前端需要在数据库维护该模板）。输入变量内容;输出生成pdf的oss路径
   * 
   */
  static testTotalPriceMaterial(body: BQuotationApplyTotalPriceMaterialDataModel): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/test/totalPriceMaterial`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 测试报价申请总价页Summary页生成pdf,模板id固定为totalPriceSummary（前端需要在数据库维护该模板）。输入变量内容;输出生成pdf的oss路径
   * 
   */
  static testTotalPriceSummary(body: BQuotationApplyTotalPriceSummaryDataModel): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`/price/genPdf/test/totalPriceSummary`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
