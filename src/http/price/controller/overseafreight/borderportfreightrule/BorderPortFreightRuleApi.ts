import ApiClient from 'http/ApiClient'
import BorderPortFreightRuleGetOut from 'model/remote/price/model/vo/overseafreight/borderportfreightrule/BorderPortFreightRuleGetOut'
import BorderPortFreightRuleListIn from 'model/remote/price/model/vo/overseafreight/borderportfreightrule/BorderPortFreightRuleListIn'
import BorderPortFreightRuleListOut from 'model/remote/price/model/vo/overseafreight/borderportfreightrule/BorderPortFreightRuleListOut'
import BorderPortFreightRuleRatioSetIn from 'model/remote/price/model/vo/overseafreight/borderportfreightrule/BorderPortFreightRuleRatioSetIn'
import BorderPortFreightRuleSaveIn from 'model/remote/price/model/vo/overseafreight/borderportfreightrule/BorderPortFreightRuleSaveIn'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import RaiseRatioSeasonGetOut from 'model/remote/price/model/vo/overseafreight/borderportfreightrule/RaiseRatioSeasonGetOut'
import { FreightRuleSeason } from 'model/remote/price/model/po/FreightRuleSeason'

export default class BorderPortFreightRuleApi {
  /**
   * 新建
   * 
   */
  static create(body: BorderPortFreightRuleSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/borderPortFreightRule/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/borderPortFreightRule/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static edit(body: BorderPortFreightRuleSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/borderPortFreightRule/edit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: BorderPortFreightRuleListIn): Promise<void> {
    return ApiClient.server().post(`/data/borderPortFreightRule/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<BorderPortFreightRuleGetOut>> {
    return ApiClient.server().get(`/data/borderPortFreightRule/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(year?: number, season?: FreightRuleSeason): Promise<LtcResponse<RaiseRatioSeasonGetOut>> {
    return ApiClient.server().get(`/data/borderPortFreightRule/getRaiseRatio`, {
      params: {
        year: year,
        season: season
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/borderPortFreightRule/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: BorderPortFreightRuleListIn): Promise<LtcPageResponse<BorderPortFreightRuleListOut>> {
    return ApiClient.server().post(`/data/borderPortFreightRule/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: BorderPortFreightRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/borderPortFreightRule/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验重复记录
   * 
   */
  static validateSameRule(body: BorderPortFreightRuleSaveIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/borderPortFreightRule/validateSameRule`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
