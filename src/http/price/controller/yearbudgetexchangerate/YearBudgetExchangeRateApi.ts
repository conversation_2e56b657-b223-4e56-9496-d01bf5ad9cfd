import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import YearBudgetExchangeRateListIn from 'model/remote/price/model/vo/yearbudgetexchangerate/YearBudgetExchangeRateListIn'
import YearBudgetExchangeRateListOut from 'model/remote/price/model/vo/yearbudgetexchangerate/YearBudgetExchangeRateListOut'
import YearBudgetExchangeRateSaveIn from 'model/remote/price/model/vo/yearbudgetexchangerate/YearBudgetExchangeRateSaveIn'

export default class YearBudgetExchangeRateApi {
  /**
   * 新建
   * 
   */
  static create(body: YearBudgetExchangeRateSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearBudgetExchangeRate/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearBudgetExchangeRate/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出年度预算汇率
   * 
   */
  static export(body: YearBudgetExchangeRateListIn): Promise<void> {
    return ApiClient.server().post(`data/yearBudgetExchangeRate/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入年度预算汇率
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/yearBudgetExchangeRate/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: YearBudgetExchangeRateListIn): Promise<LtcPageResponse<YearBudgetExchangeRateListOut>> {
    return ApiClient.server().post(`data/yearBudgetExchangeRate/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
