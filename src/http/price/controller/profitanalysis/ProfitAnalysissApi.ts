import ApiClient from 'http/ApiClient'
import BGetExchangeRateTypeRequest from 'model/remote/price/controller/profitanalysis/BGetExchangeRateTypeRequest'
import BProfitAnalysis from 'model/remote/price/controller/profitanalysis/BProfitAnalysis'
import BWholeProfitAnalysis from 'model/remote/price/controller/profitanalysis/BWholeProfitAnalysis'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import MatProfitAnalysissQueryRequest from 'model/remote/price/api/profitanalysis/MatProfitAnalysissQueryRequest'
import ProfitAnalysissExchangeRate from 'model/remote/price/api/profitanalysis/ProfitAnalysissExchangeRate'
import ProfitAnalysissQueryRequest from 'model/remote/price/api/profitanalysis/ProfitAnalysissQueryRequest'

export default class ProfitAnalysissApi {
  /**
   * 计算毛利分析
   * 
   */
  static cal(body: ProfitAnalysissQueryRequest): Promise<LtcResponse<BWholeProfitAnalysis>> {
    return ApiClient.server().post(`/price/profitanalysis/cal`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 毛利分析计算前检查
   * 
   */
  static calCheck(body: ProfitAnalysissQueryRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/profitanalysis/calCheck`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算单台的毛利分析
   * 
   */
  static calMat(body: MatProfitAnalysissQueryRequest): Promise<LtcResponse<BProfitAnalysis>> {
    return ApiClient.server().post(`/price/profitanalysis/cal/mat`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算毛利分析
   * 
   */
  static getExchangeRateType(body: BGetExchangeRateTypeRequest): Promise<LtcResponse<ProfitAnalysissExchangeRate>> {
    return ApiClient.server().post(`/price/profitanalysis/getExchangeRateType`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算毛利分析-测试使用
   * 
   */
  static save(body: ProfitAnalysissQueryRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/profitanalysis/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
