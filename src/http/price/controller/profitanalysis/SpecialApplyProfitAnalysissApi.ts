import ApiClient from 'http/ApiClient'
import BGetExchangeRateTypeRequest from 'model/remote/price/controller/profitanalysis/BGetExchangeRateTypeRequest'
import BSpecialApplyProfitAnalysis from 'model/remote/price/controller/profitanalysis/BSpecialApplyProfitAnalysis'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import SpecialApplyProfitAnalysisQueryRequest from 'model/remote/price/controller/profitanalysis/SpecialApplyProfitAnalysisQueryRequest'
import SpecialApplyProfitAnalysissExchangeRate from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysissExchangeRate'

export default class SpecialApplyProfitAnalysissApi {
  /**
   * 毛利分析计算前检查
   * 
   */
  static calCheck(body: SpecialApplyProfitAnalysisQueryRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/profitanalysis/calCheck`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 单据币种汇率查询
   * 
   */
  static getExchangeRateType(body: BGetExchangeRateTypeRequest): Promise<LtcResponse<SpecialApplyProfitAnalysissExchangeRate>> {
    return ApiClient.server().post(`/price/specialApply/profitanalysis/getExchangeRateType`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 特价申请计算毛利分析-按照物料指导价分析
   * 
   */
  static specialApplyCal(body: SpecialApplyProfitAnalysisQueryRequest): Promise<LtcResponse<BSpecialApplyProfitAnalysis>> {
    return ApiClient.server().post(`/price/specialApply/profitanalysis/cal`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
