import ApiClient from 'http/ApiClient'
import I18ProdGroupSortListIn from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortListIn'
import I18ProdGroupSortListModelBySortIn from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortListModelBySortIn'
import I18ProdGroupSortListModelBySortOut from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortListModelBySortOut'
import I18ProdGroupSortListOut from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortListOut'
import I18ProdGroupSortListTreeIn from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortListTreeIn'
import I18ProdGroupSortListTreeOut from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortListTreeOut'
import I18ProdGroupSortMaterialListIn from 'model/remote/data/model/vo/material/I18ProdGroupSortMaterialListIn'
import I18ProdGroupSortRelatedModelsIn from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortRelatedModelsIn'
import I18ProdGroupSortResortIn from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortResortIn'
import I18ProdGroupSortSaveIn from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortSaveIn'
import I18ProdGroupSortTree from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortTree'
import I18ProdGroupSortValidateSortNameIn from 'model/remote/price/model/vo/i18prodgroupsort/I18ProdGroupSortValidateSortNameIn'
import I18SortProdMaterialGetOut from 'model/remote/price/model/vo/i18prodgroupsort/I18SortProdMaterialGetOut'
import I18SortProdMaterialWithDetailGetOut from 'model/remote/price/model/vo/i18prodgroupsort/I18SortProdMaterialWithDetailGetOut'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import MaterialFirstLevelListOut from 'model/remote/data/model/vo/material/MaterialFirstLevelListOut'
import QueryI18SortIn from 'model/remote/price/model/vo/i18prodgroupsort/QueryI18SortIn'
import QueryI18SortProdModelIn from 'model/remote/price/model/vo/i18prodgroupsort/QueryI18SortProdModelIn'
import QueryI18SortProdModelOut from 'model/remote/price/model/vo/i18prodgroupsort/QueryI18SortProdModelOut'
import UcnEnVo from 'model/remote/support/core/domain/UcnEnVo'
import UcnVo from 'model/remote/support/core/domain/UcnVo'

export default class I18ProdGroupSortApi {
  /**
   * 新建
   * 
   */
  static create(body: I18ProdGroupSortSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: I18ProdGroupSortListModelBySortIn): Promise<void> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据物料号查询国际产品线分类物料
   * 
   */
  static getI18SortMaterial(code: string): Promise<LtcResponse<I18SortProdMaterialGetOut>> {
    return ApiClient.server().get(`/data/i18ProdGroupSort/getI18SortMaterial`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据物料号查询国际产品线分类物料v2
   * 
   */
  static getI18SortMaterialV2(code: string): Promise<LtcResponse<I18SortProdMaterialWithDetailGetOut>> {
    return ApiClient.server().get(`/data/i18ProdGroupSort/v2/getI18SortMaterial`, {
      params: {
        code: code
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询分类异常机型
   * 
   */
  static listAbnormalProdModel(body: I18ProdGroupSortListModelBySortIn): Promise<LtcPageResponse<UcnVo>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/listAbnormalProdModel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询分类下机型
   * 
   */
  static listProdModelBySort(body: I18ProdGroupSortListModelBySortIn): Promise<LtcPageResponse<I18ProdGroupSortListModelBySortOut>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/listProdModelBySort`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: I18ProdGroupSortListIn): Promise<LtcPageResponse<I18ProdGroupSortListOut>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询分类树
   * 
   */
  static listTree(): Promise<LtcResponse<I18ProdGroupSortListTreeOut>> {
    return ApiClient.server().get(`/data/i18ProdGroupSort/listTree`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据条件查询分类树
   * 
   */
  static listTreeByCondition(body: I18ProdGroupSortListTreeIn): Promise<LtcResponse<I18ProdGroupSortTree[]>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/listTreeByCondition`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 通用查询国际产品线分类
   * 
   */
  static queryI18Sort(body: QueryI18SortIn): Promise<LtcPageResponse<UcnEnVo>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/queryI18Sort`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 通用查询国际产品线分类物料号
   * 
   */
  static queryI18SortMaterial(body: I18ProdGroupSortMaterialListIn): Promise<LtcPageResponse<MaterialFirstLevelListOut>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/queryI18SortMaterial`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 通用查询国际产品线分类机型
   * 
   */
  static queryI18SortProdModel(body: QueryI18SortProdModelIn): Promise<LtcPageResponse<QueryI18SortProdModelOut>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/queryI18SortProdModel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 更新分类排序
   * 
   */
  static resort(body: I18ProdGroupSortResortIn[]): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/resort`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分类关联机型
   * 
   */
  static sortRelatedProdModels(body: I18ProdGroupSortRelatedModelsIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/sortRelatedProdModels`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static update(body: I18ProdGroupSortSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验分类名称中英文重复
   * 
   */
  static validateSortName(body: I18ProdGroupSortValidateSortNameIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/i18ProdGroupSort/validateSortName`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验分类下是否关联机型
   * 
   */
  static validateUsedSort(sortId: string): Promise<LtcResponse<void>> {
    return ApiClient.server().get(`/data/i18ProdGroupSort/validateUsedSort`, {
      params: {
        sortId: sortId
      }
    }).then((res) => {
      return res.data
    })
  }

}
