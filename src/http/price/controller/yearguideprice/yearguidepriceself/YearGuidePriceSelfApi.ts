import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import YearGuidePriceSaveResponse from 'model/remote/price/model/vo/YearGuidePriceSaveResponse'
import YearGuidePriceSelfGetByIdOut from 'model/remote/price/model/vo/yearguidepriceself/YearGuidePriceSelfGetByIdOut'
import YearGuidePriceSelfListIn from 'model/remote/price/model/vo/yearguidepriceself/YearGuidePriceSelfListIn'
import YearGuidePriceSelfListOut from 'model/remote/price/model/vo/yearguidepriceself/YearGuidePriceSelfListOut'
import YearGuidePriceSelfSaveIn from 'model/remote/price/model/vo/yearguidepriceself/YearGuidePriceSelfSaveIn'
import YearGuidePriceSelfVerifyEffectiveTimeOut from 'model/remote/price/model/vo/yearguidepriceself/YearGuidePriceSelfVerifyEffectiveTimeOut'

export default class YearGuidePriceSelfApi {
  /**
   * 新建
   * 
   */
  static create(body: YearGuidePriceSelfSaveIn, cover: boolean): Promise<LtcResponse<YearGuidePriceSaveResponse>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/create`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 年度指导价-自营导出
   * 
   */
  static export(body: YearGuidePriceSelfListIn): Promise<void> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<YearGuidePriceSelfGetByIdOut>> {
    return ApiClient.server().get(`data/yearGuidePriceSelf/get`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 年度指导价-自营导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: YearGuidePriceSelfListIn): Promise<LtcPageResponse<YearGuidePriceSelfListOut>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/switchEnabled`, {}, {
      params: {
        id: id,
        version: version,
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改
   * 
   */
  static update(body: YearGuidePriceSelfSaveIn, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/update`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 验证有效期-编辑
   * 
   */
  static verifyEffectiveTime(body: YearGuidePriceSelfSaveIn): Promise<LtcResponse<YearGuidePriceSelfVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/verifyEffectiveTime`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 验证有效期-列表
   * 
   */
  static verifyListEffectiveTime(id: string): Promise<LtcResponse<YearGuidePriceSelfVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`data/yearGuidePriceSelf/verifyListEffectiveTime`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

}
