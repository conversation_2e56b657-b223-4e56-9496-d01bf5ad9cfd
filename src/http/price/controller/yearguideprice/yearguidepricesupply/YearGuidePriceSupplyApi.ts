import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import YearGuidePriceSaveResponse from 'model/remote/price/model/vo/YearGuidePriceSaveResponse'
import YearGuidePriceSupplyGetByIdOut from 'model/remote/price/model/vo/yearguidepricesupply/YearGuidePriceSupplyGetByIdOut'
import YearGuidePriceSupplyListIn from 'model/remote/price/model/vo/yearguidepricesupply/YearGuidePriceSupplyListIn'
import YearGuidePriceSupplyListOut from 'model/remote/price/model/vo/yearguidepricesupply/YearGuidePriceSupplyListOut'
import YearGuidePriceSupplySaveIn from 'model/remote/price/model/vo/yearguidepricesupply/YearGuidePriceSupplySaveIn'
import YearGuidePriceSupplyVerifyEffectiveTimeOut from 'model/remote/price/model/vo/yearguidepricesupply/YearGuidePriceSupplyVerifyEffectiveTimeOut'

export default class YearGuidePriceSupplyApi {
  /**
   * 新建
   * 
   */
  static create(body: YearGuidePriceSupplySaveIn, cover: boolean): Promise<LtcResponse<YearGuidePriceSaveResponse>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/create`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id逻辑删除
   * 
   */
  static deleteById(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 年度指导价-供货导出
   * 
   */
  static export(body: YearGuidePriceSupplyListIn): Promise<void> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<YearGuidePriceSupplyGetByIdOut>> {
    return ApiClient.server().get(`data/yearGuidePriceSupply/get`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 年度指导价-供货导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: YearGuidePriceSupplyListIn): Promise<LtcPageResponse<YearGuidePriceSupplyListOut>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/switchEnabled`, {}, {
      params: {
        id: id,
        version: version,
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改
   * 
   */
  static update(body: YearGuidePriceSupplySaveIn, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/update`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 验证有效期-编辑
   * 
   */
  static verifyEffectiveTime(body: YearGuidePriceSupplySaveIn): Promise<LtcResponse<YearGuidePriceSupplyVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/verifyEffectiveTime`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 验证有效期-列表
   * 
   */
  static verifyListEffectiveTime(id: string): Promise<LtcResponse<YearGuidePriceSupplyVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`data/yearGuidePriceSupply/verifyListEffectiveTime`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

}
