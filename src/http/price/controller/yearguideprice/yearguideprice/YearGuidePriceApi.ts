import ApiClient from 'http/ApiClient'
import BYearGuidePriceBatchMatchRequest from 'model/remote/price/controller/yearguideprice/yearguideprice/BYearGuidePriceBatchMatchRequest'
import BYearGuidePriceMatchRequest from 'model/remote/price/controller/yearguideprice/yearguideprice/BYearGuidePriceMatchRequest'
import BYearGuidePriceMatchResult from 'model/remote/price/controller/yearguideprice/yearguideprice/BYearGuidePriceMatchResult'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class YearGuidePriceApi {
  /**
   * 批量计算物料的指导价(无论是否匹配到指导价，响应结果返回全部物料。未匹配时指导价、指导价币种为空)
   * 
   */
  static batchCalcGuidePrice(body: BYearGuidePriceBatchMatchRequest): Promise<LtcResponse<BYearGuidePriceMatchResult[]>> {
    return ApiClient.server().post(`data/yearGuidePrice/batchCalcGuidePrice`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算物料的指导价
   * 
   */
  static calcGuidePrice(body: BYearGuidePriceMatchRequest): Promise<LtcResponse<BYearGuidePriceMatchResult>> {
    return ApiClient.server().post(`data/yearGuidePrice/calcGuidePrice`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
