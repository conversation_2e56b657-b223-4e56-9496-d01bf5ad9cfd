import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import PackingFeeGetOut from 'model/remote/price/model/vo/sundryfee/packingfee/PackingFeeGetOut'
import PackingFeeListIn from 'model/remote/price/model/vo/sundryfee/packingfee/PackingFeeListIn'
import PackingFeeListOut from 'model/remote/price/model/vo/sundryfee/packingfee/PackingFeeListOut'
import PackingFeeSaveIn from 'model/remote/price/model/vo/sundryfee/packingfee/PackingFeeSaveIn'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'
import SundryFeeRuleRatioSetIn from 'model/remote/price/model/vo/sundryfee/SundryFeeRuleRatioSetIn'

export default class PackingFeeApi {
  /**
   * 新建
   * 
   */
  static create(body: PackingFeeSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packingFee/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packingFee/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<PackingFeeGetOut>> {
    return ApiClient.server().get(`/data/packingFee/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取柜型
   * 
   */
  static getCabinetType(): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/packingFee/getCabinetType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/packingFee/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/packingFee/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: PackingFeeListIn): Promise<LtcPageResponse<PackingFeeListOut>> {
    return ApiClient.server().post(`/data/packingFee/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: SundryFeeRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/packingFee/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static update(body: PackingFeeSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packingFee/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
