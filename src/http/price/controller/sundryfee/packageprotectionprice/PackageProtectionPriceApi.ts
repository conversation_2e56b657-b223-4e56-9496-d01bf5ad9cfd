import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import PackageProtectionPriceGetOut from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionPriceGetOut'
import PackageProtectionPriceListIn from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionPriceListIn'
import PackageProtectionPriceListOut from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionPriceListOut'
import PackageProtectionPriceQueryDetailIn from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionPriceQueryDetailIn'
import PackageProtectionPriceSaveIn from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionPriceSaveIn'

export default class PackageProtectionPriceApi {
  /**
   * 新建
   * 
   */
  static create(body: PackageProtectionPriceSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packageProtectionPrice/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packageProtectionPrice/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: PackageProtectionPriceListIn): Promise<void> {
    return ApiClient.server().post(`/data/packageProtectionPrice/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<PackageProtectionPriceGetOut>> {
    return ApiClient.server().get(`/data/packageProtectionPrice/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/packageProtectionPrice/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: PackageProtectionPriceListIn): Promise<LtcPageResponse<PackageProtectionPriceListOut>> {
    return ApiClient.server().post(`/data/packageProtectionPrice/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据条件查询详细明细
   * 
   */
  static queryDetail(body: PackageProtectionPriceQueryDetailIn): Promise<LtcResponse<PackageProtectionPriceGetOut[]>> {
    return ApiClient.server().post(`/data/packageProtectionPrice/queryDetail`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static update(body: PackageProtectionPriceSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packageProtectionPrice/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
