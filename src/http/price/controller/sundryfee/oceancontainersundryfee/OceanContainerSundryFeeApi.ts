import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import OceanContainerSundryFeeListIn from 'model/remote/price/model/vo/sundryfee/oceancontainersundryfee/OceanContainerSundryFeeListIn'
import OceanContainerSundryFeeListOut from 'model/remote/price/model/vo/sundryfee/oceancontainersundryfee/OceanContainerSundryFeeListOut'
import OceanContainerSundryFeeSaveIn from 'model/remote/price/model/vo/sundryfee/oceancontainersundryfee/OceanContainerSundryFeeSaveIn'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'
import SundryFeeRuleRatioSetIn from 'model/remote/price/model/vo/sundryfee/SundryFeeRuleRatioSetIn'

export default class OceanContainerSundryFeeApi {
  /**
   * 新建
   * 
   */
  static create(body: OceanContainerSundryFeeSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanContainerSundryFee/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanContainerSundryFee/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: OceanContainerSundryFeeListIn): Promise<void> {
    return ApiClient.server().post(`/data/oceanContainerSundryFee/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取柜型
   * 
   */
  static getCabinetType(): Promise<LtcResponse<string[]>> {
    return ApiClient.server().get(`/data/oceanContainerSundryFee/getCabinetType`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/oceanContainerSundryFee/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/oceanContainerSundryFee/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: OceanContainerSundryFeeListIn): Promise<LtcPageResponse<OceanContainerSundryFeeListOut>> {
    return ApiClient.server().post(`/data/oceanContainerSundryFee/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: SundryFeeRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/oceanContainerSundryFee/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
