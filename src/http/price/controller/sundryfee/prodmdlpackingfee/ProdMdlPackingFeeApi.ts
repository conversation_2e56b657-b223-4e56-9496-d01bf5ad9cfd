import ApiClient from 'http/ApiClient'
import BProdMdlPackingFee from 'model/remote/price/controller/sundryfee/prodmdlpackingfee/BProdMdlPackingFee'
import BProdMdlPackingFeeCreateRequest from 'model/remote/price/controller/sundryfee/prodmdlpackingfee/BProdMdlPackingFeeCreateRequest'
import BProdMdlPacking<PERSON>eeFilter from 'model/remote/price/controller/sundryfee/prodmdlpackingfee/BProdMdlPackingFeeFilter'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse2 from 'model/remote/support/core/domain/LtcPageResponse2'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class ProdMdlPackingFeeApi {
  /**
   * 新建
   * 
   */
  static create(body: BProdMdlPackingFeeCreateRequest): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/data/prodMdlPackingFee/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/prodMdlPackingFee/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 禁用
   * 
   */
  static disable(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/prodMdlPackingFee/disable`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 启用
   * 
   */
  static enable(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/prodMdlPackingFee/enable`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: BProdMdlPackingFeeFilter): Promise<void> {
    return ApiClient.server().post(`/data/prodMdlPackingFee/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<BProdMdlPackingFee>> {
    return ApiClient.server().get(`/data/prodMdlPackingFee/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/prodMdlPackingFee/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询
   * 
   */
  static query(body: BProdMdlPackingFeeFilter): Promise<LtcPageResponse2<BProdMdlPackingFee[]>> {
    return ApiClient.server().post(`/data/prodMdlPackingFee/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
