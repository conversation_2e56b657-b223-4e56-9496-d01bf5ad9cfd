import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import OceanLooseSundryFeeListIn from 'model/remote/price/model/vo/sundryfee/oceanloosesundryfee/OceanLooseSundryFeeListIn'
import OceanLooseSundryFeeListOut from 'model/remote/price/model/vo/sundryfee/oceanloosesundryfee/OceanLooseSundryFeeListOut'
import OceanLooseSundryFeeSaveIn from 'model/remote/price/model/vo/sundryfee/oceanloosesundryfee/OceanLooseSundryFeeSaveIn'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'
import SundryFeeRuleRatioSetIn from 'model/remote/price/model/vo/sundryfee/SundryFeeRuleRatioSetIn'

export default class OceanLooseSundryFeeApi {
  /**
   * 新建
   * 
   */
  static create(body: OceanLooseSundryFeeSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanLooseSundryFee/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanLooseSundryFee/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: OceanLooseSundryFeeListIn): Promise<void> {
    return ApiClient.server().post(`/data/oceanLooseSundryFee/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/oceanLooseSundryFee/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/oceanLooseSundryFee/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: OceanLooseSundryFeeListIn): Promise<LtcPageResponse<OceanLooseSundryFeeListOut>> {
    return ApiClient.server().post(`/data/oceanLooseSundryFee/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: SundryFeeRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/oceanLooseSundryFee/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
