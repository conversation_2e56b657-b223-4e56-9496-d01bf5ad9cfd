import ApiClient from 'http/ApiClient'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import OceanContainerProdModuleSundryFeeListIn from 'model/remote/price/model/vo/sundryfee/oceancontainerprodmodulesundryfee/OceanContainerProdModuleSundryFeeListIn'
import OceanContainerProdModuleSundryFeeListOut from 'model/remote/price/model/vo/sundryfee/oceancontainerprodmodulesundryfee/OceanContainerProdModuleSundryFeeListOut'
import OceanContainerProdModuleSundryFeeSaveIn from 'model/remote/price/model/vo/sundryfee/oceancontainerprodmodulesundryfee/OceanContainerProdModuleSundryFeeSaveIn'
import OceanContainerProdModuleSundryFeeVerifyEffectiveTimeOut from 'model/remote/price/model/vo/sundryfee/oceancontainerprodmodulesundryfee/OceanContainerProdModuleSundryFeeVerifyEffectiveTimeOut'

export default class OceanContainerProdModuleSundryFeeApi {
  /**
   * 新建
   * 
   */
  static create(body: OceanContainerProdModuleSundryFeeSaveIn, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanContainerProdModuleSundryFee/create`, body, {
      params: {
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanContainerProdModuleSundryFee/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: OceanContainerProdModuleSundryFeeListIn): Promise<void> {
    return ApiClient.server().post(`/data/oceanContainerProdModuleSundryFee/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: OceanContainerProdModuleSundryFeeListIn): Promise<LtcPageResponse<OceanContainerProdModuleSundryFeeListOut>> {
    return ApiClient.server().post(`/data/oceanContainerProdModuleSundryFee/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 切换启用状态
   * 
   */
  static switchEnabled(id: string, version: number, cover: boolean): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/oceanContainerProdModuleSundryFee/switchEnabled`, {}, {
      params: {
        id: id,
        version: version,
        cover: cover
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 新建-验证有效期
   * 
   */
  static verifyEffectiveTime(body: OceanContainerProdModuleSundryFeeSaveIn): Promise<LtcResponse<OceanContainerProdModuleSundryFeeVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`/data/oceanContainerProdModuleSundryFee/verifyEffectiveTime`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 启用-验证有效期
   * 
   */
  static verifyListEffectiveTime(id: string): Promise<LtcResponse<OceanContainerProdModuleSundryFeeVerifyEffectiveTimeOut[]>> {
    return ApiClient.server().post(`/data/oceanContainerProdModuleSundryFee/verifyListEffectiveTime`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

}
