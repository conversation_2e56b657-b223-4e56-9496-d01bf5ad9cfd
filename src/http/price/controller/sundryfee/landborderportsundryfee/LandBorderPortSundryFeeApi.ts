import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LandBorderPortSundryFeeListIn from 'model/remote/price/model/vo/sundryfee/landborderportsundryfee/LandBorderPortSundryFeeListIn'
import LandBorderPortSundryFeeListOut from 'model/remote/price/model/vo/sundryfee/landborderportsundryfee/LandBorderPortSundryFeeListOut'
import LandBorderPortSundryFeeSaveIn from 'model/remote/price/model/vo/sundryfee/landborderportsundryfee/LandBorderPortSundryFeeSaveIn'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import RaiseRatioGetOut from 'model/remote/price/model/vo/RaiseRatioGetOut'
import SundryFeeRuleRatioSetIn from 'model/remote/price/model/vo/sundryfee/SundryFeeRuleRatioSetIn'

export default class LandBorderPortSundryFeeApi {
  /**
   * 新建
   * 
   */
  static create(body: LandBorderPortSundryFeeSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/landBorderPortSundryFee/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/landBorderPortSundryFee/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: LandBorderPortSundryFeeListIn): Promise<void> {
    return ApiClient.server().post(`/data/landBorderPortSundryFee/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询有效期结束日期内最新加价系数
   * 
   */
  static getRaiseRatio(effectiveEndDate?: Date): Promise<LtcResponse<RaiseRatioGetOut>> {
    return ApiClient.server().get(`/data/landBorderPortSundryFee/getRaiseRatio`, {
      params: {
        effectiveEndDate: effectiveEndDate
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/landBorderPortSundryFee/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: LandBorderPortSundryFeeListIn): Promise<LtcPageResponse<LandBorderPortSundryFeeListOut>> {
    return ApiClient.server().post(`/data/landBorderPortSundryFee/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 设置加价系数
   * 
   */
  static setRaiseRatio(body: SundryFeeRuleRatioSetIn): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/landBorderPortSundryFee/setRaiseRatio`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
