import ApiClient from 'http/ApiClient'
import BPackagingProtectionPrice from 'model/remote/price/controller/sundryfee/packagingprotectionprice/BPackagingProtectionPrice'
import BPackagingProtectionPriceFilter from 'model/remote/price/controller/sundryfee/packagingprotectionprice/BPackagingProtectionPriceFilter'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse2 from 'model/remote/support/core/domain/LtcPageResponse2'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class PackagingProtectionPriceApi {
  /**
   * 新建
   * 
   */
  static create(body: BPackagingProtectionPrice): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packagingProtectionPrice/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packagingProtectionPrice/delete`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 禁用
   * 
   */
  static disable(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packagingProtectionPrice/disable`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 启用
   * 
   */
  static enable(id: string, version: number): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/data/packagingProtectionPrice/enable`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出
   * 
   */
  static export(body: BPackagingProtectionPriceFilter): Promise<void> {
    return ApiClient.server().post(`/data/packagingProtectionPrice/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static getById(id: string): Promise<LtcResponse<BPackagingProtectionPrice>> {
    return ApiClient.server().get(`/data/packagingProtectionPrice/getById`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`/data/packagingProtectionPrice/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询
   * 
   */
  static query(body: BPackagingProtectionPriceFilter): Promise<LtcPageResponse2<BPackagingProtectionPrice[]>> {
    return ApiClient.server().post(`/data/packagingProtectionPrice/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
