import ApiClient from 'http/ApiClient'
import LogisticsGroupGetByIdOut from 'model/remote/price/model/vo/logisticsgroup/LogisticsGroupGetByIdOut'
import LogisticsGroupListIn from 'model/remote/price/model/vo/logisticsgroup/LogisticsGroupListIn'
import LogisticsGroupListOut from 'model/remote/price/model/vo/logisticsgroup/LogisticsGroupListOut'
import LogisticsGroupSaveIn from 'model/remote/price/model/vo/logisticsgroup/LogisticsGroupSaveIn'
import LogisticsGroupValidateOut from 'model/remote/price/model/vo/logisticsgroup/LogisticsGroupValidateOut'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class LogisticsGroupApi {
  /**
   * 新建
   * 
   */
  static create(body: LogisticsGroupSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/logisticsGroup/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据id查询
   * 
   */
  static get(id: string): Promise<LtcResponse<LogisticsGroupGetByIdOut>> {
    return ApiClient.server().get(`data/logisticsGroup/get`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: LogisticsGroupListIn): Promise<LtcPageResponse<LogisticsGroupListOut>> {
    return ApiClient.server().post(`data/logisticsGroup/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 修改
   * 
   */
  static update(body: LogisticsGroupSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/logisticsGroup/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 验证
   * 
   */
  static validate(body: LogisticsGroupSaveIn): Promise<LtcResponse<LogisticsGroupValidateOut[]>> {
    return ApiClient.server().post(`data/logisticsGroup/validate`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
