import ApiClient from 'http/ApiClient'
import ImportRequest from 'model/remote/data/model/vo/excel/ImportRequest'
import ImportResult from 'model/remote/support/utils/excel/ImportResult'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import SeasonBudgetExchangeRateListIn from 'model/remote/price/model/vo/seasonbudgetexchangerate/SeasonBudgetExchangeRateListIn'
import SeasonBudgetExchangeRateListOut from 'model/remote/price/model/vo/seasonbudgetexchangerate/SeasonBudgetExchangeRateListOut'
import SeasonBudgetExchangeRateSaveIn from 'model/remote/price/model/vo/seasonbudgetexchangerate/SeasonBudgetExchangeRateSaveIn'

export default class SeasonBudgetExchangeRateApi {
  /**
   * 新建
   * 
   */
  static create(body: SeasonBudgetExchangeRateSaveIn): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/seasonBudgetExchangeRate/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 删除
   * 
   */
  static delete(id: string): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`data/seasonBudgetExchangeRate/delete`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导出季度预算汇率
   * 
   */
  static export(body: SeasonBudgetExchangeRateListIn): Promise<void> {
    return ApiClient.server().post(`data/seasonBudgetExchangeRate/export`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 导入季度预算汇率
   * 
   */
  static importData(body: ImportRequest): Promise<LtcResponse<ImportResult>> {
    return ApiClient.server().post(`data/seasonBudgetExchangeRate/import`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static listQuery(body: SeasonBudgetExchangeRateListIn): Promise<LtcPageResponse<SeasonBudgetExchangeRateListOut>> {
    return ApiClient.server().post(`data/seasonBudgetExchangeRate/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
