import ApiClient from 'http/ApiClient'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import ProdModelSpecialConfigDetailNewVo from 'model/remote/price/model/vo/prodmodelspecialconfig/ProdModelSpecialConfigDetailNewVo'
import ProdModelSpecialConfigGetNewOut from 'model/remote/price/model/vo/prodmodelspecialconfig/ProdModelSpecialConfigGetNewOut'
import ProdModelSpecialConfigListNewIn from 'model/remote/price/model/vo/prodmodelspecialconfig/ProdModelSpecialConfigListNewIn'
import ProdModelSpecialConfigListNewOut from 'model/remote/price/model/vo/prodmodelspecialconfig/ProdModelSpecialConfigListNewOut'
import ProdModelSpecialConfigSaveNewIn from 'model/remote/price/model/vo/prodmodelspecialconfig/ProdModelSpecialConfigSaveNewIn'

export default class ProdModelSpecialConfigApi {
  /**
   * 新建
   * 
   */
  static createSpecialTag(body: ProdModelSpecialConfigSaveNewIn): Promise<LtcResponse<boolean>> {
    return ApiClient.server().post(`/data/prodModelSpecialConfig/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据prodModelId删除
   * 
   */
  static deleteSpecialTagByModelId(prodModelId: string): Promise<LtcResponse<boolean>> {
    return ApiClient.server().post(`/data/prodModelSpecialConfig/delete`, {}, {
      params: {
        prodModelId: prodModelId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询机型下面所有的选项值
   * 
   */
  static getAllOptionValueByModelId(prodModelId: string): Promise<LtcResponse<ProdModelSpecialConfigDetailNewVo[]>> {
    return ApiClient.server().get(`/data/prodModelSpecialConfig/getAllOptionValue`, {
      params: {
        prodModelId: prodModelId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 列表查询
   * 
   */
  static getModelPage(body: ProdModelSpecialConfigListNewIn): Promise<LtcPageResponse<ProdModelSpecialConfigListNewOut>> {
    return ApiClient.server().post(`/data/prodModelSpecialConfig/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据prodModelId查询
   * 
   */
  static getSpecialTagByModelId(prodModelId: string): Promise<LtcResponse<ProdModelSpecialConfigGetNewOut>> {
    return ApiClient.server().get(`/data/prodModelSpecialConfig/get`, {
      params: {
        prodModelId: prodModelId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 编辑
   * 
   */
  static updateSpecialTag(body: ProdModelSpecialConfigSaveNewIn): Promise<LtcResponse<boolean>> {
    return ApiClient.server().post(`/data/prodModelSpecialConfig/update`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 校验机型是否已存在
   * 
   */
  static validateExistProdModel(prodModuleId: string): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/prodModelSpecialConfig/validateExistProdModel`, {}, {
      params: {
        prodModuleId: prodModuleId
      }
    }).then((res) => {
      return res.data
    })
  }

}
