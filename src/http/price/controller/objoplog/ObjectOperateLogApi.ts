import ApiClient from 'http/ApiClient'
import LatestOperateLogQueryRequestDto from 'model/remote/vo/LatestOperateLogQueryRequestDto'
import LatestOperateLogQueryResponseDto from 'model/remote/vo/LatestOperateLogQueryResponseDto'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import ObjectOperateLogDetailsQueryRequestDto from 'model/remote/vo/ObjectOperateLogDetailsQueryRequestDto'
import ObjectOperateLogDto from 'model/remote/vo/ObjectOperateLogDto'
import ObjectTypeDto from 'model/remote/vo/ObjectTypeDto'
import ObjectTypeQueryRequestDto from 'model/remote/vo/ObjectTypeQueryRequestDto'
import QueryResultDto from 'model/remote/vo/QueryResultDto'
import TaskProgressDto from 'model/remote/vo/TaskProgressDto'

// 对象操作日志服务
export default class ObjectOperateLogApi {
  /**
   * 导出具体某个对象的操作日志详情
   * 
   */
  static exportDetails(body: ObjectOperateLogDetailsQueryRequestDto): Promise<LtcResponse<TaskProgressDto>> {
    return ApiClient.server().post(`/data/objectOperateLog/exportDetails`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取任务进度
   * 
   */
  static getTaskProgress(taskName: string, taskId: string): Promise<LtcResponse<TaskProgressDto>> {
    return ApiClient.server().get(`/data/objectOperateLog/getTaskProgress`, {
      params: {
        taskName: taskName,
        taskId: taskId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 中断任务
   * 
   */
  static interrupt(taskName: string, taskId: string): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/data/objectOperateLog/interruptTaskProgress`, {}, {
      params: {
        taskName: taskName,
        taskId: taskId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询对象类型
   * 
   */
  static query(body: ObjectTypeQueryRequestDto): Promise<LtcResponse<QueryResultDto<ObjectTypeDto>>> {
    return ApiClient.server().post(`/data/objectOperateLog/queryObjectType`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询具体某个对象的操作日志详情
   * 
   */
  static queryDetails(body: ObjectOperateLogDetailsQueryRequestDto): Promise<LtcResponse<ObjectOperateLogDto[]>> {
    return ApiClient.server().post(`/data/objectOperateLog/queryDetails`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询对象最新的操作日志
   * 
   */
  static queryLatest(body: LatestOperateLogQueryRequestDto): Promise<LtcResponse<LatestOperateLogQueryResponseDto>> {
    return ApiClient.server().post(`/data/objectOperateLog/queryLatest`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
