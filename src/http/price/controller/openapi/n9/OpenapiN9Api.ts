import ApiClient from 'http/ApiClient'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import N9QuotationApplyBill from 'model/remote/price/controller/openapi/n9/N9QuotationApplyBill'
import QuotationApplyBillCalcRequest from 'model/remote/price/controller/openapi/n9/QuotationApplyBillCalcRequest'

export default class OpenapiN9Api {
  /**
   * 计算报价单
   * 
   */
  static calcQuotationApplyBills(body: QuotationApplyBillCalcRequest): Promise<LtcResponse<N9QuotationApplyBill>> {
    return ApiClient.server().post(`openapi/n9/calcQuotationApplyBills`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
