import ApiClient from 'http/ApiClient'
import PortalResponse from 'model/remote/price/controller/openapi/portal/PortalResponse'
import PortalSimpleQueryRequest from 'model/remote/price/controller/openapi/portal/PortalSimpleQueryRequest'
import TableResourceDataVo from 'model/remote/price/controller/openapi/portal/TableResourceDataVo'

export default class OpenapiPortalApi {
  /**
   * 查询国家
   * 
   */
  static queryCountry(body: PortalSimpleQueryRequest): Promise<PortalResponse<TableResourceDataVo[]>> {
    return ApiClient.server().post(`openapi/portal/queryCountry`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询产品线
   * 
   */
  static queryProdGroup(body: PortalSimpleQueryRequest): Promise<PortalResponse<TableResourceDataVo[]>> {
    return ApiClient.server().post(`openapi/portal/queryProdGroup`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询子公司
   * 
   */
  static querySubsidiary(body: PortalSimpleQueryRequest): Promise<PortalResponse<TableResourceDataVo[]>> {
    return ApiClient.server().post(`openapi/portal/querySubsidiary`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
