import ApiClient from 'http/ApiClient'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import MaterialListInOpenapi from 'model/remote/data/model/vo/material/MaterialListInOpenapi'
import MaterialListOutOpenapi from 'model/remote/data/model/vo/material/MaterialListOutOpenapi'

export default class OpenapiDmsApi {
  /**
   * 
   */
  static initMaterialInfoData(): Promise<LtcResponse<void>> {
    return ApiClient.server().get(`openapi/dms/materialInfo/mq/initData`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 
   */
  static initMaterialSaleStateData(): Promise<LtcResponse<void>> {
    return ApiClient.server().get(`openapi/dms/materialSaleState/mq/initData`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询物料信息及可售状态
   * 
   */
  static queryMaterialInfoSaleState(body: MaterialListInOpenapi): Promise<LtcResponse<MaterialListOutOpenapi[]>> {
    return ApiClient.server().post(`openapi/dms/queryMaterialInfoSaleState`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
