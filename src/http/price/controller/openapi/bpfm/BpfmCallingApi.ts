import ApiClient from 'http/ApiClient'
import BpfmBizServiceNotifyDto from 'model/remote/price/controller/openapi/bpfm/response/BpfmBizServiceNotifyDto'
import BpfmBizServiceNotifyRequest from 'model/remote/price/controller/openapi/bpfm/request/BpfmBizServiceNotifyRequest'
import BpfmGetResourceModuleRequest from 'model/remote/price/controller/openapi/bpfm/request/BpfmGetResourceModuleRequest'
import BpfmQueryResourceModuleRequest from 'model/remote/price/controller/openapi/bpfm/request/BpfmQueryResourceModuleRequest'
import BpfmResourceModuleDto from 'model/remote/price/controller/openapi/bpfm/response/BpfmResourceModuleDto'
import BpfmResourceModuleFilterCandidateRequest from 'model/remote/price/controller/openapi/bpfm/request/BpfmResourceModuleFilterCandidateRequest'
import BpfmResponse from 'model/remote/support/feign/bpfm/response/BpfmResponse'
import QueryResult from 'model/remote/vo/QueryResult'
import UCN from 'model/remote/vo/UCN'

export default class BpfmCallingApi {
  /**
   * 业务服务事件通知
   * 
   */
  static bizServiceNotify(body: BpfmBizServiceNotifyRequest, tenant?: string, orgid?: string, userid?: string, useruuid?: string, usercode?: string, username?: string): Promise<BpfmResponse<BpfmBizServiceNotifyDto>> {
    return ApiClient.server().post(`openapi/bpfm/bizservice/notify`, body, {
      headers: {
        tenant: tenant,
        orgid: orgid,
        userid: userid,
        useruuid: useruuid,
        usercode: usercode,
        username: username
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * echo
   * 
   */
  static echo(): Promise<BpfmResponse<void>> {
    return ApiClient.server().get(`openapi/bpfm/echo`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取指定资源模块详情
   * 
   */
  static getResourceModule(body: BpfmGetResourceModuleRequest, tenant?: string, orgid?: string, userid?: string, useruuid?: string, usercode?: string, username?: string): Promise<BpfmResponse<BpfmResourceModuleDto>> {
    return ApiClient.server().post(`openapi/bpfm/resourcemodule/get`, body, {
      headers: {
        tenant: tenant,
        orgid: orgid,
        userid: userid,
        useruuid: useruuid,
        usercode: usercode,
        username: username
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询资源模块
   * 
   */
  static queryResourceModule(body: BpfmQueryResourceModuleRequest, tenant?: string, orgid?: string, userid?: string, useruuid?: string, usercode?: string, username?: string): Promise<BpfmResponse<QueryResult<BpfmResourceModuleDto>>> {
    return ApiClient.server().post(`openapi/bpfm/resourcemodule/query`, body, {
      headers: {
        tenant: tenant,
        orgid: orgid,
        userid: userid,
        useruuid: useruuid,
        usercode: usercode,
        username: username
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 过滤任务候选人
   * 
   */
  static resourceModuleFilterCandidate(body: BpfmResourceModuleFilterCandidateRequest, tenant?: string, orgid?: string, userid?: string, useruuid?: string, usercode?: string, username?: string): Promise<BpfmResponse<UCN[]>> {
    return ApiClient.server().post(`openapi/bpfm/resourcemodule/filtercandidate`, body, {
      headers: {
        tenant: tenant,
        orgid: orgid,
        userid: userid,
        useruuid: useruuid,
        usercode: usercode,
        username: username
      }
    }).then((res) => {
      return res.data
    })
  }

}
