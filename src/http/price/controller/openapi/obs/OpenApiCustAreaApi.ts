import ApiClient from 'http/ApiClient'
import CustAreaListIn from 'model/remote/price/model/vo/regionmanagement/CustAreaListIn'
import CustAreaListOut from 'model/remote/price/model/vo/regionmanagement/CustAreaListOut'
import IdStringName from 'model/remote/support/core/domain/IdStringName'
import LtcPageResponse from 'model/remote/support/core/domain/LtcPageResponse'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class OpenApiCustAreaApi {
  /**
   * 查询客户区域映射关系
   * 
   */
  static list(body: CustAreaListIn): Promise<LtcPageResponse<CustAreaListOut>> {
    return ApiClient.server().post(`openapi/obs/custArea/list`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询所有大区
   * 
   */
  static listAllArea(): Promise<LtcResponse<IdStringName[]>> {
    return ApiClient.server().post(`openapi/obs/custArea/listAllArea`, {}, {
    }).then((res) => {
      return res.data
    })
  }

}
