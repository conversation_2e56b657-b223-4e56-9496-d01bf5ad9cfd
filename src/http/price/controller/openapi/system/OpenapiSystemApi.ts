import ApiClient from 'http/ApiClient'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class OpenapiSystemApi {
  /**
   * 分类缓存获取
   * 
   */
  static getSortCache(): Promise<LtcResponse<any>> {
    return ApiClient.server().get(`openapi/system/sort/cache/get`, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分类缓存刷新
   * 
   */
  static refreshSortCache(): Promise<LtcResponse<void>> {
    return ApiClient.server().get(`openapi/system/sort/cache/refresh`, {
    }).then((res) => {
      return res.data
    })
  }

}
