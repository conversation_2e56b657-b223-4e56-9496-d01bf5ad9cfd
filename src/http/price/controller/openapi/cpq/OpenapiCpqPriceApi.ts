import ApiClient from 'http/ApiClient'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import MatFeeOpenapi from 'model/remote/price/controller/quotationapply/logisticscost/MatFeeOpenapi'
import OpennapiQuotationApplyGenPdfRequest from 'model/remote/price/controller/genpdf/OpennapiQuotationApplyGenPdfRequest'
import QuotationApplyBillCreateRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyBillCreateRequestOpenapi'
import QuotationApplyBillOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyBillOpenapi'
import QuotationApplyCalcLogisticsFeesRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcLogisticsFeesRequestOpenapi'
import QuotationApplyCalcOtherFeesRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcOtherFeesRequestOpenapi'
import QuotationApplyCalcPlanRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcPlanRequestOpenapi'
import QuotationApplyCalcPlanResultOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcPlanResultOpenapi'
import UploadResult from 'model/remote/support/core/file/UploadResult'
import YearGuidePriceSaveOpenapi from 'model/remote/price/model/vo/yearguidepriceself/YearGuidePriceSaveOpenapi'

export default class OpenapiCpqPriceApi {
  /**
   * 计算物流费用
   * 
   */
  static calcLogisticsFees(body: QuotationApplyCalcLogisticsFeesRequestOpenapi): Promise<LtcResponse<MatFeeOpenapi[]>> {
    return ApiClient.server().post(`openapi/cpq/price/quotationApply/calcLogisticsFees`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算其他费用
   * 
   */
  static calcOtherFees(body: QuotationApplyCalcOtherFeesRequestOpenapi): Promise<LtcResponse<MatFeeOpenapi[]>> {
    return ApiClient.server().post(`openapi/cpq/price/quotationApply/calcOtherFees`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算装运方案、防护方案
   * 
   */
  static calcPlan(body: QuotationApplyCalcPlanRequestOpenapi): Promise<LtcResponse<QuotationApplyCalcPlanResultOpenapi>> {
    return ApiClient.server().post(`openapi/cpq/price/quotationApply/calcPlan`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价申请创建
   * 
   */
  static create(body: QuotationApplyBillCreateRequestOpenapi): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`openapi/cpq/price/quotationApply/bill/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价申请下载pdf。输入报价申请导出信息;输出生成pdf的oss路径
   * 
   */
  static downloadQuotationApply(body: OpennapiQuotationApplyGenPdfRequest): Promise<LtcResponse<UploadResult>> {
    return ApiClient.server().post(`openapi/cpq/price/quotationApply/download`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价申请详情查询
   * 
   */
  static get(id: string): Promise<LtcResponse<QuotationApplyBillOpenapi>> {
    return ApiClient.server().get(`openapi/cpq/price/quotationApply/bill/get`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存年度指导价-自营/供货
   * 
   */
  static saveGuidePrice(body: YearGuidePriceSaveOpenapi): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`openapi/cpq/price/yearGuidePrice/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
