import ApiClient from 'http/ApiClient'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import MatFeeOpenapi from 'model/remote/price/controller/quotationapply/logisticscost/MatFeeOpenapi'
import QuotationApplyBillCreateRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyBillCreateRequestOpenapi'
import QuotationApplyCalcLogisticsFeesRequestOpenapiV2 from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcLogisticsFeesRequestOpenapiV2'
import QuotationApplyCalcOtherFeesRequestOpenapiV2 from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcOtherFeesRequestOpenapiV2'
import QuotationApplyCalcPlanRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcPlanRequestOpenapi'
import QuotationApplyCalcPlanResultOpenapiV2 from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcPlanResultOpenapiV2'

export default class OpenapiCpqPriceApiV2Controller {
  /**
   * 计算物流费用
   * 
   */
  static calcLogisticsFees(body: QuotationApplyCalcLogisticsFeesRequestOpenapiV2): Promise<LtcResponse<MatFeeOpenapi[]>> {
    return ApiClient.server().post(`openapi/cpq/priceV2/quotationApply/calcLogisticsFees`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算其他费用
   * 
   */
  static calcOtherFees(body: QuotationApplyCalcOtherFeesRequestOpenapiV2): Promise<LtcResponse<MatFeeOpenapi[]>> {
    return ApiClient.server().post(`openapi/cpq/priceV2/quotationApply/calcOtherFees`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算装运方案、防护方案
   * 
   */
  static calcPlan(body: QuotationApplyCalcPlanRequestOpenapi): Promise<LtcResponse<QuotationApplyCalcPlanResultOpenapiV2>> {
    return ApiClient.server().post(`openapi/cpq/priceV2/quotationApply/calcPlan`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 报价申请创建
   * 
   */
  static create(body: QuotationApplyBillCreateRequestOpenapi): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`openapi/cpq/priceV2/quotationApply/bill/create`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
