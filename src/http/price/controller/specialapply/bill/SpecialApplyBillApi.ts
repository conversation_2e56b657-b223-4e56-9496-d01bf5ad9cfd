import ApiClient from 'http/ApiClient'
import ApplyDescription from 'model/remote/price/api/specialapply/bill/ApplyDescription'
import BpfmHiTaskInstVO from 'model/remote/price/model/vo/bpfm/BpfmHiTaskInstVO'
import CompleteSpecialApplyBillUserTaskRequest from 'model/remote/price/api/specialapply/bill/CompleteSpecialApplyBillUserTaskRequest'
import IdStringName from 'model/remote/support/core/domain/IdStringName'
import LtcPageResponse2 from 'model/remote/support/core/domain/LtcPageResponse2'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import SpecialApplyBill from 'model/remote/price/api/specialapply/bill/SpecialApplyBill'
import SpecialApplyBillBatchCancelRequest from 'model/remote/price/api/specialapply/bill/SpecialApplyBillBatchCancelRequest'
import SpecialApplyBillFilter from 'model/remote/price/api/specialapply/bill/SpecialApplyBillFilter'
import SpecialApplyCurrencyExchangeRate from 'model/remote/price/api/specialapply/bill/SpecialApplyCurrencyExchangeRate'
import SpecialApplyFeeDetail from 'model/remote/price/api/specialapply/priceoverview/SpecialApplyFeeDetail'
import SpecialApplyMatLine from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLine'
import SpecialApplyMatLineExwPriceRequest from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLineExwPriceRequest'
import SpecialApplyMatRequest from 'model/remote/price/api/specialapply/mat/SpecialApplyMatRequest'
import SpecialApplyMatResponse from 'model/remote/price/api/specialapply/mat/SpecialApplyMatResponse'
import SpecialApplyPriceOverviewMatLineV2 from 'model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewMatLineV2'
import SpecialApplyPriceOverviewSaveRequestV2 from 'model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewSaveRequestV2'
import SpecialApplyPriceSubmitRequest from 'model/remote/price/api/specialapply/bill/SpecialApplyPriceSubmitRequest'
import SpecialApplySupplementPrice from 'model/remote/price/api/specialapply/priceoverview/SpecialApplySupplementPrice'
import SpecialApplySupplementPriceSubmitRequest from 'model/remote/price/api/specialapply/priceoverview/SpecialApplySupplementPriceSubmitRequest'

export default class SpecialApplyBillApi {
  /**
   * 作废
   * 
   */
  static abort(body: SpecialApplyBillBatchCancelRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/abort`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 取消
   * 
   */
  static cancel(body: SpecialApplyBillBatchCancelRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/cancel`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 完成用户任务。回传查询返回的任务出口数据
   * 
   */
  static completeUserTask(body: CompleteSpecialApplyBillUserTaskRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/completeUserTask`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 确认并应用
   * 
   */
  static confirmApplyPriceOverview(id: string, version: number): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/confirmApplyPriceOverview`, {}, {
      params: {
        id: id,
        version: version
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询报价申请单基本信息
   * 
   */
  static get(id: string): Promise<LtcResponse<SpecialApplyBill>> {
    return ApiClient.server().get(`/price/specialApply/bill/get`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 特价申请说明查询
   * 
   */
  static getApplyDescription(billId: string): Promise<LtcResponse<ApplyDescription>> {
    return ApiClient.server().get(`/price/specialApply/bill/getApplyDescription`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据流程ID查询报价申请单基本信息
   * 
   */
  static getByApproveProcessId(approveProcessId: string): Promise<LtcResponse<SpecialApplyBill>> {
    return ApiClient.server().get(`/price/specialApply/bill/getByApproveProcessId`, {
      params: {
        approveProcessId: approveProcessId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取费用明细
   * 
   */
  static getFeeDetails(billId: string): Promise<LtcResponse<SpecialApplyFeeDetail>> {
    return ApiClient.server().get(`/price/specialApply/bill/getFeeDetails`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取物料指导价
   * 
   */
  static getMatExwPrice(body: SpecialApplyMatLineExwPriceRequest): Promise<LtcResponse<SpecialApplyMatLine>> {
    return ApiClient.server().post(`/price/specialApply/bill/mat/getExwPrice`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据单号查询物料明细
   * 
   */
  static getMatLine(billId: string): Promise<LtcResponse<SpecialApplyMatLine[]>> {
    return ApiClient.server().get(`/price/specialApply/bill/getMatLine`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据单号查询物料明细V2版本。额外包括：物料申请类型
   * 
   */
  static getMatLineV2(billId: string): Promise<LtcResponse<SpecialApplyMatResponse>> {
    return ApiClient.server().get(`/price/specialApply/bill/getMatLineV2`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 根据单号特价总览
   * 
   */
  static getPriceOverview(billId: string): Promise<LtcResponse<SpecialApplyPriceOverviewMatLineV2[]>> {
    return ApiClient.server().get(`/price/specialApply/bill/getPriceOverview`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取补充价格信息
   * 
   */
  static getSupplementPrice(billId: string): Promise<LtcResponse<SpecialApplySupplementPrice>> {
    return ApiClient.server().get(`/price/specialApply/bill/supplementPrice/get`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取币种及汇率
   * 
   */
  static listCurrencyExchangeRate(billId: string): Promise<LtcResponse<SpecialApplyCurrencyExchangeRate>> {
    return ApiClient.server().get(`/price/specialApply/bill/listCurrencyExchangeRate`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询历史任务
   * 
   */
  static listHistoryTask(id: string): Promise<LtcResponse<BpfmHiTaskInstVO[]>> {
    return ApiClient.server().get(`/price/specialApply/bill/queryHistoryTask`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取产品线负责人负责的产品线
   * 
   */
  static listOwnerProductGroupId(id: string): Promise<LtcResponse<IdStringName[]>> {
    return ApiClient.server().get(`/price/specialApply/bill/listOwnerProductGroupId`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 分页查询
   * 
   */
  static query(body: SpecialApplyBillFilter): Promise<LtcPageResponse2<SpecialApplyBill[]>> {
    return ApiClient.server().post(`/price/specialApply/bill/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存
   * 
   */
  static save(body: SpecialApplyBill): Promise<LtcResponse<string>> {
    return ApiClient.server().post(`/price/specialApply/bill/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存并关闭
   * 
   */
  static saveAndClose(body: SpecialApplyPriceOverviewSaveRequestV2): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/saveAndClose`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 补充价格信息保存并提交
   * 
   */
  static saveAndSubmitSupplementPrice(body: SpecialApplySupplementPriceSubmitRequest): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/price/specialApply/bill/supplementPrice/saveAndSubmit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 完成并提交提交审核
   * 
   */
  static saveAndsubmit(body: SpecialApplyPriceOverviewSaveRequestV2): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/price/specialApply/bill/saveAndsubmit`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存物料明细
   * 
   */
  static saveMatLine(body: SpecialApplyMatRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/saveMatLine`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存特价总览明细
   * 
   */
  static savePriceOverview(body: SpecialApplyPriceOverviewSaveRequestV2): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/savePriceOverview`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存补充价格信息
   * 
   */
  static saveSupplementPrice(body: SpecialApplySupplementPrice): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/supplementPrice/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 提交审核
   * 
   */
  static submit(body: SpecialApplyPriceSubmitRequest): Promise<LtcResponse<string[]>> {
    return ApiClient.server().post(`/price/specialApply/bill/submit`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
