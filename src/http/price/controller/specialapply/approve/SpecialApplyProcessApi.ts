import ApiClient from 'http/ApiClient'
import BSaveCostPriceRequest from 'model/remote/price/controller/quotationapply/approve/BSaveCostPriceRequest'
import BSaveSpecialApplyBillDiscountRequest from 'model/remote/price/controller/specialapply/approve/BSaveSpecialApplyBillDiscountRequest'
import BSpecialApplyAdjustRequest from 'model/remote/price/controller/specialapply/approve/BSpecialApplyAdjustRequest'
import BSpecialApplyReviewRequest from 'model/remote/price/controller/specialapply/approve/BSpecialApplyReviewRequest'
import BpfmUserTaskInfo from 'model/remote/price/controller/specialapply/approve/BpfmUserTaskInfo'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class SpecialApplyProcessApi {
  /**
   * 申请人调整
   * 
   */
  static adjust(body: BSpecialApplyAdjustRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/approve/adjust`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 查询“价格管理委员会审批”的审批结果信息
   * 
   */
  static listBpfmUserTaskInfosForPriceManagement(id: string): Promise<LtcResponse<BpfmUserTaskInfo[]>> {
    return ApiClient.server().get(`/price/specialApply/approve/listBpfmUserTaskInfos/priceManagement`, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 价格专员归档
   * 
   */
  static review(body: BSpecialApplyReviewRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/approve/review`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 审批填写后续是否允许折扣
   * 
   */
  static saveBillDiscount(body: BSaveSpecialApplyBillDiscountRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/approve/saveBillDiscount`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 填写成本价
   * 
   */
  static saveCostPrice(body: BSaveCostPriceRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/approve/saveCostPrice`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
