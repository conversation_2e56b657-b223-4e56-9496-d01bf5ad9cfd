import ApiClient from 'http/ApiClient'
import LtcPageResponse2 from 'model/remote/support/core/domain/LtcPageResponse2'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import SpecialApplyApprovalInformation from 'model/remote/price/api/specialapply/bill/SpecialApplyApprovalInformation'
import SpecialApplyApprovalInformationFilter from 'model/remote/price/api/specialapply/bill/SpecialApplyApprovalInformationFilter'

export default class SpecialApplyBillInformationApi {
  /**
   * 意见查询
   * 
   */
  static queryApprovalInformation(body: SpecialApplyApprovalInformationFilter): Promise<LtcPageResponse2<SpecialApplyApprovalInformation[]>> {
    return ApiClient.server().post(`/price/specialApply/information/query`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 意见删除
   * 
   */
  static remove(id: string): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/information/remove`, {}, {
      params: {
        id: id
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 意见保存
   * 
   */
  static save(body: SpecialApplyApprovalInformation): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/information/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
