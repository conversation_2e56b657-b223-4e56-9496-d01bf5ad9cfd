import ApiClient from 'http/ApiClient'
import CalMatLineOtherFee from 'model/remote/price/api/specialapply/priceoverview/CalMatLineOtherFee'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'

export default class SpecialApplyPriceOverviewApi {
  /**
   * 物料折扣率变更或综合特价变更重新计算单条物料数据
   * 
   */
  static calMatOtherFee(body: CalMatLineOtherFee): Promise<LtcResponse<CalMatLineOtherFee>> {
    return ApiClient.server().post(`/price/specialApply/bill/price/calMatLineOtherFee`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
