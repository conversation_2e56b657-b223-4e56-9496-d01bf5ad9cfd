import ApiClient from 'http/ApiClient'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import SpecialApplyLogisticsExpensesMatInfo from 'model/remote/price/api/specialapply/logisticsexpenses/SpecialApplyLogisticsExpensesMatInfo'
import SpecialApplyLogisticsExpensesSummaryResponse from 'model/remote/price/controller/specialapply/logisticsexpenses/SpecialApplyLogisticsExpensesSummaryResponse'

export default class SpecialApplyBillLogisticsExpensesApi {
  /**
   * 获取物流费用汇总信息
   * 
   */
  static getExpensesSummary(billId: string): Promise<LtcResponse<SpecialApplyLogisticsExpensesSummaryResponse>> {
    return ApiClient.server().get(`/price/specialApply/bill/logisticsExpenses/expensesSummary/getBillId`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取物料信息
   * 
   */
  static getMatInfo(billId: string): Promise<LtcResponse<SpecialApplyLogisticsExpensesMatInfo>> {
    return ApiClient.server().get(`/price/specialApply/bill/logisticsExpenses/mat/get`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存物料信息
   * 
   */
  static saveMatInfo(body: SpecialApplyLogisticsExpensesMatInfo): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/logisticsExpenses/mat/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
